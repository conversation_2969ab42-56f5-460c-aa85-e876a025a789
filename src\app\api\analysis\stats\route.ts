import { NextResponse } from 'next/server'
import { StrategyService } from '../services/StrategyService'
import { ApiResponseHandler } from '@/lib/api/response'

export async function POST(request: Request) {
  try {
    const { backtest_id } = await request.json()
    
    const stats = await StrategyService.getStrategyStats(backtest_id)
    if (stats) {
      return ApiResponseHandler.success(stats)
    }else {
      throw new Error(`没有找到${backtest_id}对应的策略`)
    }
   
  } catch (error) {
    return ApiResponseHandler.error(error instanceof Error ? error.message : 'Internal Server Error')
  }
}