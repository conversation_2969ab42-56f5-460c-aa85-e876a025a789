import { ApiResponseHandler } from '@/lib/api/response';
import { strategyStatsRepository } from '../repositories/strategyStatsRepositories';

export async function GET() {
  try {
    const data = await strategyStatsRepository.getStrategyCount();
    
    return ApiResponseHandler.success({
      list: data.map(item => ({
        strategy_name: item.strategy_name,
        num: parseInt(item.num as any)
      }))
    });
  } catch (error) {
    console.error('Failed to get strategy count:', error);
    return ApiResponseHandler.error('获取策略统计失败');
  }
}