import { FEATURE_TYPE, MODEL_ALGORITHM } from "@/constants";

/**
 * AI分析任务状态枚举
 */
enum TaskStatus {
  NotStarted = 0, // 未开始
  InProgress = 1, // 进行中
  Completed = 2, // 已完成
  Paused = 3, // 已暂停
}

/**
 * 技术指标类型（对应technical_indicators字段的JSON结构）
 */
interface TechnicalIndicator {
  category: string;
  indicators: Array<{
    name: string;
    description?: string;
    types?: string[];
    components?: string[];
    function?: string;
    signals?: Record<string, string | number>;
    // 其他可能的动态字段...
  }>;
}

/**
 * AI分析任务表主接口
 */
interface AiAnalysisTask {
  id: number;
  analysis_target: number;
  analysis_period: number;
  target_name: string;
  models: number[];
  market_ids: string;
  expert_num: number;
  model_titles: string[];
  market_names: string[];
  analysis_purpost: number;
  technical_indicators: string[];
  model_algorithm: keyof typeof MODEL_ALGORITHM;
  feature_type:keyof typeof FEATURE_TYPE;
  feature_normalization: 0 | 1;
  task_status: number;
  create_time: string;
  update_time: string;
  train_start_time: string;
  train_end_time: string;
  test_start_time: string;
  test_end_time: string;
}

export { type AiAnalysisTask, type TechnicalIndicator, TaskStatus };
