import { NextRequest } from 'next/server';
import { ApiResponseHandler } from '@/lib/api/response';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const period = searchParams.get('period') || '1d';
    const limit = searchParams.get('limit') || '500';
    const start_date = searchParams.get('start_date');
    const end_date = searchParams.get('end_date');

    if (!code) {
      return ApiResponseHandler.error('股票代码不能为空', 400);
    }

    // 构建外部API请求URL - 这里使用示例API，实际使用时需要替换为真实的数据源
    const apiUrl = new URL('http://api.waizaowang.com/doc/getWatchStockTimeKLine');
    
    // 根据代码长度判断市场类型
    const marketType = code.length === 5 ? '3' : '1'; // 5位数字为港股，6位为A股
    
    apiUrl.searchParams.set('type', marketType);
    apiUrl.searchParams.set('code', code);
    apiUrl.searchParams.set('export', '1');
    apiUrl.searchParams.set('token', 'ab96d998a83e00d5ffe028b012c0b405'); // 示例token
    apiUrl.searchParams.set('fields', 'price,tdate,zdfd,zded,cjl,cje,hslv,zg,zd,kp');
    
    // 设置时间周期
    const periodMap: Record<string, string> = {
      '1m': '1',
      '5m': '5', 
      '15m': '15',
      '30m': '30',
      '1h': '60',
      '1d': 'day',
      '1w': 'week',
      '1M': 'month'
    };
    
    if (periodMap[period]) {
      apiUrl.searchParams.set('period', periodMap[period]);
    }
    
    if (limit) {
      apiUrl.searchParams.set('limit', limit);
    }

    const response = await fetch(apiUrl.toString(), {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    });

    if (!response.ok) {
      return ApiResponseHandler.error(`外部API请求失败: ${response.status}`, response.status);
    }

    const rawData = await response.text();
    
    try {
      const jsonData = JSON.parse(rawData);
      
      if (jsonData.code !== 200) {
        return ApiResponseHandler.error(jsonData.message || '获取数据失败');
      }

      // 转换数据格式
      const klineData = jsonData.data?.map((item: any) => ({
        timestamp: new Date(item.tdate).getTime(),
        open: parseFloat(item.kp) || 0,
        high: parseFloat(item.zg) || 0,
        low: parseFloat(item.zd) || 0,
        close: parseFloat(item.price) || 0,
        volume: parseInt(item.cjl) || 0,
        turnover: parseFloat(item.cje) || 0,
      })) || [];

      // 获取股票基本信息
      const stockInfo = {
        code: code,
        name: jsonData.name || code,
        market: code.startsWith('6') ? 'SH' : code.startsWith('0') || code.startsWith('3') ? 'SZ' : 'HK',
        type: 'stock' as const,
      };

      const result = {
        ...stockInfo,
        data: klineData,
        total: klineData.length,
      };

      return ApiResponseHandler.success(result);
      
    } catch (parseError) {
      console.error('JSON解析错误:', parseError);
      return ApiResponseHandler.error('数据格式错误');
    }

  } catch (error) {
    console.error('获取K线数据失败:', error);
    return ApiResponseHandler.error('服务器内部错误');
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { codes, period = '1d' } = body;

    if (!codes || !Array.isArray(codes) || codes.length === 0) {
      return ApiResponseHandler.error('股票代码列表不能为空', 400);
    }

    // 批量获取多个股票的K线数据
    const promises = codes.map(async (code: string) => {
      try {
        const apiUrl = new URL('http://api.waizaowang.com/doc/getWatchStockTimeKLine');
        const marketType = code.length === 5 ? '3' : '1';
        
        apiUrl.searchParams.set('type', marketType);
        apiUrl.searchParams.set('code', code);
        apiUrl.searchParams.set('export', '1');
        apiUrl.searchParams.set('token', 'ab96d998a83e00d5ffe028b012c0b405');
        apiUrl.searchParams.set('fields', 'price,tdate,zdfd,zded,cjl,cje');
        apiUrl.searchParams.set('limit', '1'); // 只获取最新数据

        const response = await fetch(apiUrl.toString());
        const rawData = await response.text();
        const jsonData = JSON.parse(rawData);

        if (jsonData.code === 200 && jsonData.data?.length > 0) {
          const latest = jsonData.data[0];
          return {
            code: code,
            name: jsonData.name || code,
            price: parseFloat(latest.price) || 0,
            change: parseFloat(latest.zdfd) || 0,
            change_percent: parseFloat(latest.zded) || 0,
            volume: parseInt(latest.cjl) || 0,
            turnover: parseFloat(latest.cje) || 0,
            timestamp: new Date(latest.tdate).getTime(),
          };
        }
        return null;
      } catch (error) {
        console.error(`获取${code}数据失败:`, error);
        return null;
      }
    });

    const results = await Promise.all(promises);
    const validResults = results.filter(result => result !== null);

    return ApiResponseHandler.success(validResults);

  } catch (error) {
    console.error('批量获取数据失败:', error);
    return ApiResponseHandler.error('服务器内部错误');
  }
}
