import { NextRequest } from 'next/server';
import { ApiResponseHandler } from '@/lib/api/response';
import { getHistoryKLineData } from './repository';
import moment from 'moment';

export async function GET(request: NextRequest) {
  try {
    // 获取请求参数
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const ticker = searchParams.get('ticker');
    const period = searchParams.get('period');

    console.log(type, ticker, period);

    // 参数验证
    if (!type || !ticker || !period) {
      return ApiResponseHandler.error('缺少必要参数', 400);
    }

    const start_date = searchParams.get('start_date') || '';
    const end_date = searchParams.get('end_date') || '';

    // 获取历史K线数据
    const data = await getHistoryKLineData(type, ticker, period, start_date, end_date);


    // 转换数据格式
    const klineData = data?.map((item: any) => ({
      timestamp: moment(item.date).valueOf(),
      open: parseFloat(item.open),
      high: parseFloat(item.high),
      low: parseFloat(item.low),
      close: parseFloat(item.close),
      volume: parseFloat(item.volume),
      turnover: parseFloat(item.turnover || 0)
    })) || [];

    return ApiResponseHandler.success({
      type,
      ticker,
      period,
      data: klineData,
      total: klineData.length
    });

  } catch (error) {
    console.error('获取K线历史数据失败:', error);
    return ApiResponseHandler.error('服务器内部错误');
  }
}

