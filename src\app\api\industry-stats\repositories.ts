import pool from "@/lib/quantDB";

export interface IndustryStats {
  industry: string;
  stock_num: number;
  sum_total_trades: number;
  sum_profitable_trades: number;
  sum_losing_trades: number;
  sum_total_profit_pct: number;
  avg_win_rate: number;
}

export async function getIndustryStats(
  backtestId: string
): Promise<IndustryStats[]> {
  const query = `
  SELECT industry, COUNT(*) AS stock_num, SUM(total_trades) AS sum_total_trades, SUM(profitable_trades) AS sum_profitable_trades, SUM(losing_trades) AS sum_losing_trades, SUM(total_profit_pct) AS sum_total_profit_pct, AVG(win_rate) AS avg_win_rate FROM (
SELECT 
    t.stock_code, 
    b.stock_name,
    b.industry,
    b.total_market_value,
    b.circulating_market_value,
    COUNT(*) AS total_trades,
    SUM(t.profit_pct) AS total_profit_pct,
    SUM(CASE WHEN t.profit_pct > 0 THEN 1 ELSE 0 END) AS profitable_trades,
    SUM(CASE WHEN t.profit_pct < 0 THEN 1 ELSE 0 END) AS losing_trades,
    ROUND(
        (SUM(CASE WHEN t.profit_pct > 0 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)),
        2
    ) AS win_rate
FROM 
    strategy_trades t
LEFT JOIN 
    stock_basic_info b ON t.stock_code = b.stock_code
WHERE 
    t.backtest_id = ?
GROUP BY 
    t.stock_code
ORDER BY 
    win_rate DESC
) AS t1 GROUP BY industry ORDER BY avg_win_rate DESC;
  `;

  try {
    const [rows] = await pool.execute(query, [backtestId]);
    return rows as IndustryStats[];
  } catch (error) {
    console.error("Failed to get industry statistics:", error);
    throw error;
  }
}
