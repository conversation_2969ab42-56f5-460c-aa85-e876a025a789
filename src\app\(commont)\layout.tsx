"use client";

import React, { useEffect, useState } from "react";
import { Button, ConfigProvider, Layout, Space, Switch } from "antd";
import { ThemeProvider, useTheme } from "@/contexts/ThemeContext";
import classNames from "classnames";
import { themeColors } from "../constants";
import { Menu } from "antd";
import { useRouter, usePathname } from "next/navigation";
import locale from "antd/locale/zh_CN";
import dayjs from "dayjs";
import { FullscreenOutlined, FullscreenExitOutlined } from "@ant-design/icons";

import "dayjs/locale/zh-cn";
dayjs.locale("zh-cn");

const { Header, Content } = Layout;

const LayoutContent: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { isDark, toggleTheme } = useTheme();
  const [isMounted, setIsMounted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  const menuItems = [
    // {
    //   key: "/factor",
    //   label: "因子库列表",
    //   onClick: () => router.push("/factor"),
    // },
    {
      key: "/strategy_stats",
      label: "策略分析",
      onClick: () => router.push("/strategy_stats"),
    },
    // {
    //   key: "/task_analysis",
    //   label: <a href="/task_analysis">量化分析</a>,
    //   onClick: () => router.push("/task_analysis"),
    // },
    // {
    //   key: "/feature_analysis",
    //   label: <a href="/feature_analysis">数据中心</a>,
    //   onClick: () => router.push("/feature_analysis"),
    // },
    {
      key: "/financial_news",
      label: <a href="/financial_news">股市分析</a>,
      onClick: () => router.push("/financial_news"),
    },
    // {
    //   key: "/kline",
    //   label: <a href="/kline">K线图分析</a>,
    //   onClick: () => router.push("/kline"),
    // },
  ];

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
    };
  }, []);

  if (!isMounted) {
    return null;
  }

  return (
    <ConfigProvider
      locale={locale}
      theme={{
        components: isDark
          ? {
              Button: {
                colorBgContainer: themeColors.bg[100],
                colorText: themeColors.text[200],
                colorTextDisabled: themeColors.text[200],
                colorBorder: themeColors.accent[200],
                colorPrimary: themeColors.primary[100],
                colorPrimaryHover: themeColors.primary[200],
                colorPrimaryActive: themeColors.primary[300],
              },
              Checkbox: {
                colorPrimary: themeColors.primary[100],
                colorPrimaryHover: themeColors.primary[200],
                colorText: themeColors.text[100],
              },
              Collapse: {
                colorBgContainer: themeColors.bg[200],
                colorBorder: themeColors.accent[100],
                colorText: themeColors.text[200],
                colorTextHeading: themeColors.text[100],
                headerBg: themeColors.bg[200],
              },
              DatePicker: {
                cellActiveWithRangeBg: themeColors.accent[200],
                colorBgContainer: themeColors.bg[300],
                colorBgElevated: themeColors.bg[300],
                colorBorder: themeColors.accent[100],
                colorPrimary: themeColors.primary[100],
                colorPrimaryHover: themeColors.primary[200],
                colorText: themeColors.text[100],
                colorTextDisabled: themeColors.text[200],
                colorTextHeading: themeColors.text[100],
                colorTextPlaceholder: themeColors.text[100],
              },
              Descriptions: {
                colorBgContainer: themeColors.bg[200],
                colorBorder: themeColors.accent[100],
                colorSplit: themeColors.accent[100],
                colorText: themeColors.text[200],
                colorTextHeading: themeColors.text[100],
                colorTextSecondary: themeColors.text[200],
                labelBg: themeColors.bg[100],
                labelColor: themeColors.text[100],
              },
              Divider: {
                colorSplit: themeColors.text[200],
              },
              Empty: {
                colorTextDescription: themeColors.text[200],
              },
              Input: {
                activeBorderColor: themeColors.accent[200],
                colorBgContainer: themeColors.bg[300],
                colorBorder: themeColors.accent[100],
                colorText: themeColors.text[200],
                colorTextBase: themeColors.text[200],
                colorTextPlaceholder: themeColors.text[200],
              },
              InputNumber: {
                colorBgContainer: themeColors.bg[300],
                colorBorder: themeColors.accent[100],
                colorText: themeColors.text[200],
                colorTextBase: themeColors.text[200],
                colorTextPlaceholder: themeColors.text[200],
              },
              Layout: {
                bodyBg: themeColors.bg[300],
                colorBgContainer: themeColors.bg[100],
                colorText: themeColors.text[200],
                headerBg: "#001529",
              },
              Menu: {
                colorBgContainer: themeColors.bg[200],
                colorText: themeColors.text[200],
                darkItemSelectedBg: "transparent",
              },
              Modal: {
                contentBg: themeColors.bg[100],
                titleColor: themeColors.text[100],
                headerBg: themeColors.bg[100],
                colorText: themeColors.text[100],
                colorTextHeading: themeColors.text[100],
              },
              Pagination: {
                colorBgTextActive: themeColors.accent[200],
                colorBorder: themeColors.accent[100],
                colorBorderBg: themeColors.accent[200],
                colorPrimary: themeColors.text[200],
                colorPrimaryHover: themeColors.primary[300],
                colorText: themeColors.text[100],
                colorTextDisabled: themeColors.text[200],
                itemActiveBg: themeColors.primary[100],
                itemBg: themeColors.bg[200],
                itemInputBg: themeColors.bg[200],
              },
              Popover: {
                colorBgElevated: themeColors.bg[200],
                colorBorder: themeColors.accent[100],
                colorText: themeColors.text[200],
                colorTextHeading: themeColors.text[100],
              },
              Segmented: {
                trackBg: themeColors.bg[200],
                itemActiveBg: themeColors.accent[100],
                itemColor: themeColors.text[200],
                itemSelectedColor: themeColors.primary[100],
                itemHoverColor: themeColors.primary[200],
                itemSelectedBg: themeColors.bg[100],
              },
              Select: {
                activeBorderColor: themeColors.accent[200],
                colorBgContainer: themeColors.bg[300],
                colorBgElevated: themeColors.bg[100],
                colorBorder: themeColors.accent[100],
                colorText: themeColors.text[200],
                colorTextBase: themeColors.text[200],
                colorTextPlaceholder: themeColors.text[200],
                multipleItemBg: themeColors.bg[100],
                optionSelectedBg: themeColors.primary[100],
                optionSelectedFontWeight: 500,
              },
              Table: {
                borderColor: themeColors.accent[200],
                colorBgContainer: themeColors.bg[300],
                colorIcon: themeColors.text[200],
                colorText: themeColors.text[200],
                colorTextHeading: themeColors.text[100],
                colorTextSecondary: themeColors.text[200],
                headerBg: themeColors.bg[200],
                headerSortActiveBg: themeColors.bg[300],
                headerSortHoverBg: themeColors.accent[200],
                rowHoverBg: themeColors.accent[200],
                stickyScrollBarBg: themeColors.accent[200],
              },
              Typography: {
                colorText: themeColors.text[200],
              },
              Card: {
                colorBgContainer: themeColors.bg[200],
                colorBorderSecondary: themeColors.accent[100],
                colorText: themeColors.text[200],
                colorTextHeading: themeColors.text[100],
              },
              List: {
                colorBgContainer: themeColors.bg[200],
                colorBorder: themeColors.accent[100],
                colorText: themeColors.text[200],
                colorTextHeading: themeColors.text[100],
              },
              Tabs: {
                colorBgContainer: themeColors.bg[200],
                colorBorder: themeColors.accent[100],
                colorText: themeColors.text[200],
                colorTextHeading: themeColors.text[100],
                itemSelectedColor: themeColors.primary[100],
                itemHoverColor: themeColors.primary[200],
              },
              Form: {
                colorText: themeColors.text[200],
                colorTextHeading: themeColors.text[100],
                colorTextLabel: themeColors.text[200],
                colorTextDescription: themeColors.text[200],
              },
              Radio: {
                // colorPrimary: themeColors.text[100],
                // colorPrimaryHover: themeColors.text[200],
                colorText: themeColors.text[100],
                buttonBg: themeColors.bg[300],
                buttonSolidCheckedBg: themeColors.primary[100],
                // buttonCheckedBg: themeColors.primary[100],
              },
              Tag: {
                colorBgContainer: themeColors.bg[200],
                colorBorder: themeColors.accent[100],
                colorText: themeColors.text[200],
              },
              Alert: {
                colorBgContainer: themeColors.bg[200],
                colorBorder: themeColors.accent[100],
                colorText: themeColors.text[200],
                colorIcon: themeColors.text[100],
              },
            }
          : {
              Layout: {
                bodyBg: "#f8fafc",
                colorBgContainer: "#f8fafc",
                colorText: "rgba(0, 0, 0, 0.8)",
                headerBg: "#f8fafc",
              },
              Menu: {
                colorBgContainer: "#f8fafc",
                colorText: "rgba(0, 0, 0, 0.8)",
              },
            },
        token: {
          colorPrimary: themeColors.primary[100],
        },
      }}
    >
      <Layout
        style={{
          minHeight: "100vh",
        }}
      >
        <Header className="p-0 flex justify-end items-center pr-6 border dark:border-dark-accent-100 ">
          <ConfigProvider
            theme={{
              components: {
                Switch: {
                  colorBgBase: themeColors.bg[100],
                  colorPrimary: themeColors.bg[100],
                  colorBgContainer: themeColors.accent[100],
                  colorPrimaryHover: themeColors.accent[100],
                },
              },
            }}
          >
            <Menu
              mode="horizontal"
              selectedKeys={[pathname]}
              className={classNames("flex-1 border-0", {
                "[&_.ant-menu-item-selected_.ant-menu-title-content]:bg-gradient-to-l [&_.ant-menu-title-content]:from-[#030712] [&_.ant-menu-title-content]:to-[#0056C9]":
                  isDark,
                "[&_.ant-menu-item-selected_.ant-menu-title-content]:border-[#0872FF] [&_.ant-menu-item-selected_.ant-menu-title-content]:border-2":
                  isDark,
                "[&_.ant-menu-title-content]:py-2 [&_.ant-menu-title-content]:px-4 [&_.ant-menu-title-content]:rounded-lg":
                  true,
              })}
              items={menuItems}
              theme={isDark ? "dark" : "light"}
            />
            <Space>
              <Switch
                title="切换主题"
                onChange={toggleTheme}
                checked={isDark}
                checkedChildren="🌙"
                unCheckedChildren="☀️"
                className={isDark ? "!bg-black" : "!bg-white"}
              />
              <Button
                title={isFullscreen ? "退出全屏" : "全屏"}
                icon={
                  !isFullscreen ? (
                    <FullscreenOutlined className="text-lg" />
                  ) : (
                    <FullscreenExitOutlined className="text-lg" />
                  )
                }
                type="text"
                onClick={() => {
                  if (!document.fullscreenElement) {
                    document.documentElement.requestFullscreen();
                  } else {
                    document.exitFullscreen();
                  }
                }}
              />
            </Space>
          </ConfigProvider>
        </Header>
        <Content
          className={classNames(
            isDark ? "bg-dark-bg-300 text-dark-text-100" : "bg-gray-50", // 改为更柔和的背景色
            "h-full py-6 px-10"
          )}
        >
          {children}
        </Content>
      </Layout>
    </ConfigProvider>
  );
};

const App: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ThemeProvider>
      <LayoutContent>{children}</LayoutContent>
    </ThemeProvider>
  );
};

export default App;
