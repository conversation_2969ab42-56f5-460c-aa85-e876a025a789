import { <PERSON><PERSON>, Button, Input, Modal } from "antd";
import { sendMessage } from "@/utils/AIService";
import React, { useState } from "react";
import { ArrowUpOutlined } from "@ant-design/icons";
import MarkdownReader from "./MarkdownReader";
import { useTheme } from "@/contexts/ThemeContext";

function AIModal({
  systemPrompt,
  notice,
}: {
  systemPrompt?: string;
  notice?: React.ReactNode;
}) {
  const [input, setInput] = useState("");
  const [inputVisible, setInputVisible] = useState(false);
  const { isDark } = useTheme();
  const [messages, setMessages] = useState<
    Array<{
      role: "user" | "assistant";
      content: string;
    }>
  >([]);
  const [loading, setLoading] = useState(false);

  const handleSend = async () => {
    if (loading) return;

    const userMessage = { role: "user" as const, content: input };
    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setLoading(true);

    try {
      await sendMessage(
        [
          {
            role: "user",
            content: systemPrompt || "你是一个AI助手",
          },
          userMessage,
        ],
        (content) => {
          setMessages((prev) => {
            const lastMessage = prev[prev.length - 1];
            if (lastMessage?.role === "assistant") {
              // 更新最后一条消息
              return [
                ...prev.slice(0, -1),
                { role: "assistant", content: lastMessage.content + content },
              ];
            }
            // 添加新消息
            return [...prev, { role: "assistant", content }];
          });
        }
      );
    } catch (error) {
      console.error("发送消息失败:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`flex flex-col h-full gap-4 p-4 ${isDark ? "dark" : ""}`}>
      <div className="flex-1 overflow-auto rounded-lg space-y-4 dark:bg-dark-bg-300">
        <div className="flex flex-col gap-2 min-h-[300px] p-4">
          {messages.map((msg, index) => (
            <div
              key={index}
              className={`p-3 rounded-lg shadow-sm transition-all duration-300  ${
                msg.role === "user"
                  ? "bg-blue-50 dark:bg-dark-bg-100 dark:text-dark-text-200 ml-12 mr-4"
                  : "bg-gray-50 dark:bg-dark-bg-100 dark:text-dark-text-200 ml-4 mr-12"
              } ${
                loading &&
                index === messages.length - 1 &&
                msg.role === "assistant"
                  ? "animate-pulse"
                  : ""
              }`}
            >
              <MarkdownReader content={msg.content} />
              {loading &&
                index === messages.length - 1 &&
                msg.role === "assistant" && (
                  <span className="inline-block w-2 h-4 ml-1 bg-gray-400 animate-blink" />
                )}
            </div>
          ))}
        </div>
        {/* <Alert 
          className="mx-4 shadow-sm dark:bg-dark-bg-200 dark:border-dark-bg-300" 
          message={notice ?? 'AI分析'} 
          type="info"
        /> */}
      </div>
      <div className="flex gap-2 w-full">
        {!inputVisible ? (
          <>
            <Button
              type="primary"
              size="large"
              className="flex-1 transition-all duration-300"
              onClick={() => {
                setInput("开始分析");
                handleSend();
              }}
              loading={loading}
            >
              开始分析
            </Button>
            <Button
              type="default"
              size="large"
              className="flex-[0.2] transition-all duration-300"
              onClick={() => {
                setInputVisible(true);
              }}
            >
              我要提问
            </Button>
          </>
        ) : (
          <div className="flex gap-2 w-full animate-expand">
            <Input
              type="text"
              autoFocus
              autoComplete="off"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onPressEnter={handleSend}
              placeholder="请输入内容..."
              size="large"
              className="flex-1"
              disabled={loading}
              suffix={
                <Button
                  shape="circle"
                  type="primary"
                  onClick={handleSend}
                  loading={loading}
                >
                  <ArrowUpOutlined style={{ fontSize: "18px" }} />
                </Button>
              }
            />
          </div>
        )}
      </div>
    </div>
  );
}

const useAIModal = ({
  systemPrompt,
  title,
}: {
  systemPrompt?: string;
  title?: string;
}): [() => void, React.ReactNode] => {
  const [modal, contextHolder] = Modal.useModal();
  const open = () => {
    modal.info({
      title: (
        <span className="dark:text-dark-text-100">{title ?? "AI分析"}</span>
      ),
      content: <AIModal systemPrompt={systemPrompt} />,
      width: 800,
      footer: null,
      icon: null,
      closable: true,
    });
  };

  return [open, contextHolder];
};

export default useAIModal;
