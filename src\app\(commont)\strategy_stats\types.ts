import { MarketType } from "./constants";

export interface StrategyStats {
  id: number;
  strategy_name: string;
  test_batch_id: string;
  start_date: string;
  end_date: string;
  period: number;
  market_type: MarketType;

  // 交易统计
  total_trades: number;
  total_won_trades: number;
  total_lost_trades: number;
  overall_win_rate: number;
  profit_factor: number;
  trade_efficiency: number; // 每天每次交易的平均盈利效率

  // 收益统计
  total_net_pnl: number;
  avg_net_pnl: number;
  avg_total_return: number;
  avg_max_drawdown: number;
  avg_sharpe_ratio: number;

  // 持仓统计
  avg_holding_days: number;
  trade_frequency: number;

  // 股票统计
  total_stocks: number;
  test_stocks_count: number;

  // 策略参数
  strategy_params: any; // JSON 类型
  entry_price_type: string;
  entry_wait_bars: number;
  take_profit_type: string;
  take_profit_percentage: number;
  stop_loss_percentage: number;
  min_below_mid_bars: number;
  min_volatility_pct: number;

  // 成本统计
  total_commission: number;

  // 时间戳
  created_at: string;
  updated_at: string;

  star?: 1 | 0; // 星标状态，1表示已星标，0表示未星标
}
