"use client";

import React, { useEffect, useMemo, useState } from "react";
import { <PERSON>pp, <PERSON><PERSON>, Config<PERSON><PERSON><PERSON>, Form, message, Spin } from "antd";
import TaskTable from "./components/TaskTable";
import TaskForm from "./components/TaskForm";
import {
  createTaskAnalysisData,
  getModelOptions,
  getTargetOptions,
  getTaskAnalysisData,
  StockTarget,
  updateTaskAnalysisData,
} from "./services/taskAnalysisService";
import { AiAnalysisTask } from "@/types/ai-analysis";
import { Metadata } from "next";




interface IndicatorData {
  name: string;
  description?: string;
  types?: string[];
  components?: string[];
  function?: string;
  purpose?: string;
  signals?: any;
  method?: string;
}

//
export default function Page() {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [data, setData] = useState<Partial<AiAnalysisTask[]>>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [options, setOptions] = useState<StockTarget[]>([]);
  const [modelOptions, setModelOptions] = useState<any[]>([]);
  // const [indicatorData, setIndicatorData] = useState<{
  //   [key: string]: IndicatorData[];
  // }>({});
  const targetOptions = useMemo(() => {
    if (!options) return [];

    return Object.values(options)
      .flat()
      ?.map((item) => ({
        value: item.id,
        label: item.name,
      }));
  }, [options]);

  useEffect(() => {
    const loadTargets = async () => {
      try {
        const options = await getTargetOptions();
        setOptions(options);
      } catch (error) {
        console.error("加载分析目标失败:", error);
      }
    };

    const loadModels = async () => {
      try {
        const models = await getModelOptions();
        setModelOptions(
          models.map((model) => ({
            value: model.id,
            label: model.title,
            model: model.model,
          }))
        );
      } catch (error) {
        console.error("加载模型选项失败:", error);
      }
    };

    // const loadIndicators = async () => {
    //   try {
    //     const response = await fetch("/indicators.json");
    //     const data = await response.json();
    //     const formattedData: { [key: string]: IndicatorData[] } = {};

    //     data.technical_indicators.forEach((category: any) => {
    //       formattedData[category.category] = category.indicators;
    //     });

    //     setIndicatorData(formattedData);
    //   } catch (error) {
    //     console.error("加载指标数据失败:", error);
    //   }
    // };

    // loadIndicators();
    loadTargets();
    loadModels();
  }, []);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 100,
    total: 0,
  });

  const fetchData = async () => {
    try {
      setLoading(true);
      const result = await getTaskAnalysisData({
        page: pagination.current,
        pageSize: pagination.pageSize,
      });
      setPagination((prev) => ({
        ...prev,
        total: result.pagination.total || 0,
      }));
      setData(result.data);
    } catch (error) {
      console.error("获取数据失败:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [pagination.current, pagination.pageSize]);

  return (
    <App>
      <div className="w-full">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-xl font-bold">分析任务</h1>
          <Button type="primary" onClick={() => setIsModalOpen(true)}>
            添加任务
          </Button>
          <TaskForm
            form={form}
            open={isModalOpen}
            onCancel={() => {
              setIsModalOpen(false);
              form.resetFields();
            }}
            onSubmit={async (values) => {
              try {
                if (values?.id) {
                  await updateTaskAnalysisData(values);
                  message.success("更新任务成功");
                } else {
                  await createTaskAnalysisData(values);
                  message.success("添加任务成功");
                }
                setIsModalOpen(false);
                // 刷新数据
                await fetchData();
              } catch (error: any) {
                console.error("操作任务失败:", error);
                message.error(
                  error?.message ||
                    (values?.id ? "更新任务失败" : "添加任务失败")
                );
                throw error;
              }
            }}
            targetOptions={targetOptions}
            options={options}
            modelOptions={modelOptions}
            searchTarget={async (keyword: string) => {
              try {
                const result = await getTargetOptions(keyword);
                setOptions(result);
              } catch (error) {
                console.error("搜索目标失败:", error);
              }
            }}
            // indicatorData={indicatorData}
          />
        </div>
        <div className="mt-4">
          <TaskTable
            data={data as AiAnalysisTask[]}
            pagination={pagination}
            form={form}
            loading={loading}
            setOpen={setIsModalOpen}
            fetchData={fetchData}
            onPaginationChange={(page, pageSize) => {
              setPagination((prev) => ({
                ...prev,
                current: page,
                pageSize,
              }));
            }}
          />
        </div>
      </div>
    </App>
  );
}


