{"name": "trader-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@types/react-syntax-highlighter": "^15.5.13", "ahooks": "^3.8.4", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "classnames": "^2.5.1", "echarts": "^5.6.0", "klinecharts": "^9.8.12", "lodash": "^4.17.21", "moment": "^2.30.1", "mysql2": "^3.14.0", "next": "14.2.28", "openai": "^4.94.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "styled-components": "^6.1.19", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "15.2.4", "postcss": "^8.5.3", "typescript": "^5"}}