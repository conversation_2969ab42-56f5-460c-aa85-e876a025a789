import pool from "@/lib/quantDB";

interface IcQueryParams {
  factorId: number;
  page: number;
  pageSize: number;
  sortField?:
    | "symbol_type"
    | "ic"
    | "mean_ic"
    | "total_samples"
    | "positive_ratio"
    | "ic_std"
    | "ir"
    | "t_stat"
    | "p_value"
    | 'ic_count';
  sortOrder?: 'ASC' | 'DESC';
}

export class FactorDetailsRepository {
  async count(factorId: number): Promise<number> {
    const sql = `
      SELECT COUNT(*) as total 
      FROM factor_ic 
      WHERE factor_id = ?
    `;
    const [result] = await pool.query(sql, [factorId]);
    return (result as any)[0].total;
  }

  async findIcListById(params: IcQueryParams) {
    let sql = `
      SELECT *
      FROM factor_ic
      WHERE factor_id = ? 
    `;
    const values = [params.factorId];

    if (params.sortField && params.sortOrder) {
      sql += ` ORDER BY ${params.sortField} ${params.sortOrder}`;
    } else {
      sql += ' ORDER BY created_at DESC';
    }

    sql += ' LIMIT ? OFFSET ?';
    values.push(params.pageSize, (params.page - 1) * params.pageSize);

    const [rows] = await pool.query(sql, values);
    return rows;
  }
}

export const factorDetailsRepository = new FactorDetailsRepository();

export interface FactorQueryParams {
  page: number;
  pageSize: number;
  name?: string;
  type?: string;
  id: number;
  holding_period: number | null;
  sortField?:
    | "symbol_type"
    | "ic"
    | "mean_ic"
    | "total_samples"
    | "positive_ratio"
    | "ic_std"
    | "ir"
    | "t_stat"
    | "p_value"
    | 'ic_count';
  sortOrder?: "ASC" | "DESC";
}

export class FactorRepository {
  async count(params: Pick<FactorQueryParams, "id">): Promise<number> {
    const sql = `
      SELECT COUNT(*) as total 
      FROM factor_ic
      WHERE factor_id = ?
    `;
    const values = [params.id];

    const [countResult] = await pool.query(sql, values);
    return (countResult as any)[0].total;
  }

  async findById(params: FactorQueryParams) {
    let sql = `
    SELECT
        a.feature_name, 
        f.*
    FROM
        ai_analysis_features a
    JOIN
        factor_ic f ON a.id = f.factor_id
    WHERE
        a.id = ? 
    `;
    const values = [params.id];
    if(!!params.holding_period) {
      sql += " AND f.holding_period = ?";
      values.push(params.holding_period);
    }
    if (params.sortField && params.sortOrder) {
      sql += ` ORDER BY ${params.sortField} ${params.sortOrder}`;
    } else {
      sql += ' ORDER BY created_at DESC';
    }

    sql += " LIMIT ? OFFSET ?";
    values.push(params.pageSize, (params.page - 1) * params.pageSize);

    const [rows] = await pool.query(sql, values);
    return rows;
  }
}

export const factorRepository = new FactorRepository();
