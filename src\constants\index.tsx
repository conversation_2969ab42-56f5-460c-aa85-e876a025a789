enum STOCK_TYPE {
  A = "A股",
  H = "H股",
  US = "美股",
  INDICE = "指数",
  CURRENCY = "货币",
  FUTURE = "期货",
  CRYPTO = "数字货币",
}

const TARGET_TYPE = {
  0: "原始值",
  1: "收益率",
  2: "涨跌",
  3: "带震荡的涨跌",
};

const MODEL_ALGORITHM = {
  random_forest: "随机森林",
  lstm: "LSTM神经网络",
};

const FEATURE_TYPE = {
  1: "特征工程",
  2: "原始数据常规指标",
};

const FORECAST_STATUS  =["失败","未知","成功"]


export {
  STOCK_TYPE,
  TARGET_TYPE,
  MODEL_ALGORITHM,
  FEATURE_TYPE,
  FORECAST_STATUS,
};
