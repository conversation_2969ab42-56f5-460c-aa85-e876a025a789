import { ApiResponseHandler } from "@/lib/api/response";
import pool from "@/lib/quantDB";

export interface IndustrySectionStats {
  industry: string;
  total_num: number;
  win_rate: number;
}

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const backtestId = searchParams.get("backtest_id");

  if (!backtestId) {
    return ApiResponseHandler.error("Missing required parameter: backtestId", 400);
  }

  try {
    const query = `
      SELECT 
        i.industry, 
        COUNT(*) as total_num,
        ROUND(
          (SUM(CASE WHEN t.profit_pct > 0 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)),
          2
        ) AS win_rate 
      FROM stock_basic_info as i
      LEFT JOIN strategy_trades as t ON i.stock_code = t.stock_code
      WHERE t.backtest_id = ?
      GROUP BY i.industry
      ORDER BY win_rate DESC
    `;

    const [rows] = await pool.execute(query, [backtestId]);
    
    // 计算所有板块的总体统计
    const allSections = {
      industry: "全部",
      total_num: (rows as Array<any>).reduce((sum: number, row: any) => sum + row.total_num, 0),
      win_rate:  null
    };

    return ApiResponseHandler.success([allSections, ... (rows as Array<any>)]);
  } catch (error) {
    console.error("Failed to get industry section stats:", error);
    return ApiResponseHandler.error("Failed to get industry section stats");
  }
}