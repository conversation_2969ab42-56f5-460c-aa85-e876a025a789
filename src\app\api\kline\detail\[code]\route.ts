import { NextRequest } from 'next/server';
import { ApiResponseHandler } from '@/lib/api/response';

export async function GET(
  request: NextRequest,
  { params }: { params: { code: string } }
) {
  try {
    const { code } = params;

    if (!code) {
      return ApiResponseHandler.error('股票代码不能为空', 400);
    }

    // 模拟股票详细信息数据库
    const stockDetailMap: Record<string, any> = {
      '600519': {
        code: '600519',
        name: '贵州茅台',
        market: 'SH',
        type: 'stock',
        industry: '白酒',
        concept: ['白酒', '消费', '高端白酒', '国企改革'],
        listing_date: '2001-08-27',
        total_shares: 1256000000,
        float_shares: 1256000000,
        market_cap: 2112480000000,
        pe_ratio: 28.5,
        pb_ratio: 8.2,
        dividend_yield: 1.2,
        description: '贵州茅台酒股份有限公司主要从事茅台酒及系列酒的生产和销售。',
        website: 'http://www.moutaichina.com',
        address: '贵州省仁怀市茅台镇',
        employees: 26000,
        main_business: '白酒生产销售',
      },
      '000858': {
        code: '000858',
        name: '五粮液',
        market: 'SZ',
        type: 'stock',
        industry: '白酒',
        concept: ['白酒', '消费', '国企改革'],
        listing_date: '1998-04-27',
        total_shares: 3868000000,
        float_shares: 3868000000,
        market_cap: 497032000000,
        pe_ratio: 18.6,
        pb_ratio: 3.8,
        dividend_yield: 2.1,
        description: '宜宾五粮液股份有限公司主要从事五粮液及系列酒的生产和销售。',
        website: 'http://www.wuliangye.com.cn',
        address: '四川省宜宾市翠屏区',
        employees: 20000,
        main_business: '白酒生产销售',
      },
      '300750': {
        code: '300750',
        name: '宁德时代',
        market: 'SZ',
        type: 'stock',
        industry: '新能源',
        concept: ['新能源汽车', '锂电池', '储能', '动力电池'],
        listing_date: '2018-06-11',
        total_shares: 4653000000,
        float_shares: 4653000000,
        market_cap: 861522000000,
        pe_ratio: 35.2,
        pb_ratio: 6.8,
        dividend_yield: 0.8,
        description: '宁德时代新能源科技股份有限公司专注于新能源汽车动力电池系统、储能系统的研发、生产和销售。',
        website: 'https://www.catl.com',
        address: '福建省宁德市蕉城区',
        employees: 120000,
        main_business: '动力电池系统、储能系统',
      },
      '600036': {
        code: '600036',
        name: '招商银行',
        market: 'SH',
        type: 'stock',
        industry: '银行',
        concept: ['银行', '金融', '零售银行'],
        listing_date: '2002-04-09',
        total_shares: ***********,
        float_shares: ***********,
        market_cap: 902792000000,
        pe_ratio: 5.8,
        pb_ratio: 0.9,
        dividend_yield: 3.2,
        description: '招商银行股份有限公司是中国境内第一家完全由企业法人持股的股份制商业银行。',
        website: 'https://www.cmbchina.com',
        address: '广东省深圳市福田区',
        employees: 110000,
        main_business: '商业银行业务',
      },
      '00700': {
        code: '00700',
        name: '腾讯控股',
        market: 'HK',
        type: 'stock',
        industry: '互联网',
        concept: ['互联网', '游戏', '社交', '云计算', '金融科技'],
        listing_date: '2004-06-16',
        total_shares: 9560000000,
        float_shares: 9560000000,
        market_cap: 3059200000000,
        pe_ratio: 22.1,
        pb_ratio: 3.2,
        dividend_yield: 0.4,
        description: '腾讯控股有限公司是中国领先的互联网增值服务提供商之一。',
        website: 'https://www.tencent.com',
        address: '中国深圳市南山区',
        employees: 116000,
        main_business: '互联网及相关服务',
      },
    };

    // 获取股票详细信息
    let stockDetail = stockDetailMap[code];
    
    if (!stockDetail) {
      // 如果没有预设数据，生成基础信息
      stockDetail = {
        code: code,
        name: getNameByCode(code),
        market: getMarketByCode(code),
        type: getTypeByCode(code),
        industry: '未知',
        concept: [],
        listing_date: '2000-01-01',
        total_shares: Math.floor(Math.random() * 5000000000) + 1000000000,
        float_shares: Math.floor(Math.random() * 5000000000) + 1000000000,
        market_cap: Math.floor(Math.random() * ***********00) + ***********,
        pe_ratio: Number((Math.random() * 50 + 5).toFixed(2)),
        pb_ratio: Number((Math.random() * 10 + 0.5).toFixed(2)),
        dividend_yield: Number((Math.random() * 5).toFixed(2)),
        description: `${getNameByCode(code)}是一家在${getMarketByCode(code)}交易所上市的公司。`,
        website: '',
        address: '中国',
        employees: Math.floor(Math.random() * 100000) + 1000,
        main_business: '主营业务',
      };
    }

    // 添加实时财务指标
    const financialMetrics = {
      roe: Number((Math.random() * 30 + 5).toFixed(2)), // 净资产收益率
      roa: Number((Math.random() * 15 + 2).toFixed(2)), // 总资产收益率
      gross_margin: Number((Math.random() * 50 + 20).toFixed(2)), // 毛利率
      net_margin: Number((Math.random() * 30 + 5).toFixed(2)), // 净利率
      debt_ratio: Number((Math.random() * 60 + 10).toFixed(2)), // 资产负债率
      current_ratio: Number((Math.random() * 3 + 0.5).toFixed(2)), // 流动比率
      quick_ratio: Number((Math.random() * 2 + 0.3).toFixed(2)), // 速动比率
      eps: Number((Math.random() * 10 + 0.1).toFixed(2)), // 每股收益
      bps: Number((Math.random() * 50 + 5).toFixed(2)), // 每股净资产
      revenue_growth: Number(((Math.random() - 0.5) * 50).toFixed(2)), // 营收增长率
      profit_growth: Number(((Math.random() - 0.5) * 100).toFixed(2)), // 利润增长率
    };

    const result = {
      ...stockDetail,
      financial_metrics: financialMetrics,
      update_time: new Date().toISOString(),
    };

    return ApiResponseHandler.success(result);

  } catch (error) {
    console.error('获取股票详情失败:', error);
    return ApiResponseHandler.error('服务器内部错误');
  }
}

// 辅助函数
function getNameByCode(code: string): string {
  const nameMap: Record<string, string> = {
    '600519': '贵州茅台',
    '000858': '五粮液',
    '300750': '宁德时代',
    '600036': '招商银行',
    '000001': '平安银行',
    '002415': '海康威视',
    '600887': '伊利股份',
    '300059': '东方财富',
    '688981': '中芯国际',
    '00700': '腾讯控股',
    '09988': '阿里巴巴-SW',
    '03690': '美团-W',
  };
  
  return nameMap[code] || `股票${code}`;
}

function getMarketByCode(code: string): string {
  if (code.startsWith('6') || code.startsWith('688')) return 'SH';
  if (code.startsWith('0') || code.startsWith('3')) return 'SZ';
  if (code.length === 5) return 'HK';
  return 'SH';
}

function getTypeByCode(code: string): string {
  if (code.startsWith('51') || code.startsWith('159')) return 'fund';
  if (code === '000001' || code === '399001' || code === '399006') return 'index';
  return 'stock';
}
