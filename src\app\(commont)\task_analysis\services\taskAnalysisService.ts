import { Expert } from "@/app/api/task_analysis/experts/repositories";
import { STOCK_TYPE } from "@/constants";
import { AiAnalysisTask } from "@/types/ai-analysis";
import request from "@/utils/request";
import { Feature } from "../components/FeatureDetailModal";
import { ChartData } from "../models/kline-modal/KlineChart";

interface TaskAnalysisParams {
  page: number;
  pageSize: number;
}

export interface ModelOption {
  id: number;
  title: string;
  model: string;
}

export interface StockTarget {
  code: string;
  name: string;
  type: keyof typeof STOCK_TYPE;
  id: number;
}

export type TaskDataType = Partial<
  Omit<
    AiAnalysisTask,
    "task_status" | "update_time" | "create_time" | "stock_codes_map"
  >
>;

export type UpdateTaskDataType = Partial<
  Omit<
    AiAnalysisTask,
    "task_status" | "update_time" | "create_time" | "stock_codes_map"
  >
>;

export interface MonthlyStats {
  total_count: number;
  month_period: string;
  success_count: number;
  success_rate_percent: number;
}

export interface TrainedModelType {
  id: number;
  task_id: number;
  target_type: 0 | 1 | 2 | 3;
  sequence_length: number;
  hidden_size: number;
  num_layers: number;
  learning_rate: number;
  batch_size: number;
  create_time: string;
  update_time: string;
  forecast_count: number;
  success_count: string;
  failure_count: string;
  success_rate: string;
  last_train_time?: any;
  forecast_time?: any;
  forecast_outcomes?: any;
  forecast_probability?: any;
  monthly_stats?: MonthlyStats[];
}

export interface ForecastType {
  id: number;
  task_id: number;
  model_id: number;
  model_name: string;
  feature_list: string[];
  last_train_time: string;
  forecast_time: string;
  forecast_outcomes: string;
  forecast_probability: string;
  actual_outcomes?: any;
  forecast_status: number;
  report_file?: any;
  create_time: string;
  update_time: string;
  target_type: number;
}

export async function getTaskAnalysisData(params: TaskAnalysisParams): Promise<
  {
    data: AiAnalysisTask[];
  } & {
    pagination: {
      total: number;
      pageSize: number;
      current: number;
      totalPages: number;
    };
  }
> {
  try {
    return await request.get("/task_analysis", { params });
  } catch (error) {
    console.error("获取分析任务数据失败:", error);
    throw error;
  }
}

export async function getExperts(params: {
  page: number;
  pageSize: number;
  task_id?: string;
}): Promise<{ data: Expert[]; pagination: { total: number } }> {
  try {
    return await request.get("/task_analysis/experts", { params });
  } catch (error) {
    console.error("获取专家数据失败:", error);
    throw error;
  }
}

export async function getModelOptions(): Promise<ModelOption[]> {
  try {
    return await request.get("/task_analysis/models");
  } catch (error) {
    console.error("获取模型选项失败:", error);
    throw error;
  }
}

export async function createTaskAnalysisData(taskData: TaskDataType) {
  try {
    return await request.post("/task_analysis", taskData);
  } catch (error) {
    console.error("创建任务失败:", error);
    throw error;
  }
}

export async function getTargetOptions(
  keyword?: string
): Promise<StockTarget[]> {
  try {
    return await request.get("/task_analysis/targets", { params: { keyword } });
  } catch (error) {
    console.error("获取目标选项失败:", error);
    throw error;
  }
}

export async function updateTaskAnalysisData(taskData: UpdateTaskDataType) {
  try {
    return await request.put("/task_analysis", taskData);
  } catch (error) {
    console.error("更新任务失败:", error);
    throw error;
  }
}

export async function deleteTaskAnalysisData(id: number) {
  try {
    return await request.delete(`/task_analysis?id=${id}`);
  } catch (error) {
    console.error("删除任务失败:", error);
    throw error;
  }
}

export async function getFeatures(params: {
  task_id: number;
  page: number;
  pageSize: number;
}) {
  try {
    return await request.get<
      any,
      {
        data: Feature[];
        pagination: {
          total: number;
          pageSize: number;
          current: number;
          totalPages: number;
        };
      }
    >("/task_analysis/feature", { params });
  } catch (error) {
    console.error("获取特征数据失败:", error);
    throw error;
  }
}

export async function getTrainedModals(id: number,page:number,pageSize:number) {
  try {
    return await request.get<
      any,
      {
        data: TrainedModelType[];
        pagination: {
          total: number;
          pageSize: number;
          current: number;
          totalPages: number;
        };
      }
    >(`/task_analysis/trained-model?task_id=${id}`,{
      params:{
        page,
        pageSize
      }
    });
  } catch (error) {
    console.error("删除特征数据失败:", error);
    throw error;
  }
}

export async function getForecastData(id: number, page: number, pageSize: number) {
  try {
    return await request.get<any,{
      data: ForecastType[];
      pagination: {
        total: number;
        pageSize: number;
        current: number;
        totalPages: number;
      };
    }>(`/task_analysis/forecast?model_id=${id}`,{
      params:{
        page,
        pageSize
      }
    });
  } catch (error) {
    console.error("获取预测数据失败:", error);
    throw error;
  }
}


export async function getModelKline(id: number) {
  try {
    return await request.get<any,  ChartData >(
      `/task_analysis/forecast/kline?model_id=${id}`
    );
  } catch (error) {
    console.error("获取模型K线数据失败:", error);
    throw error;
  }
}