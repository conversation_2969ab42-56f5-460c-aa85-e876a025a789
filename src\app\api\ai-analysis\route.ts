// 当前的实现存在几个问题：

// 1. 内存泄漏：全局变量会一直占用内存，且不会被垃圾回收
// 2. 多用户混淆：所有用户共享同一个 messageHistory，会导致上下文混乱
// 3. 服务器重启丢失：服务器重启后历史记录会丢失
// import { Redis } from "@upstash/redis";

// const redis = new Redis({
//   url: process.env.UPSTASH_REDIS_REST_URL!,
//   token: process.env.UPSTASH_REDIS_REST_TOKEN!,
// });
// async function getMessageHistory(sessionId: string): Promise<ChatCompletionMessageParam[]> {
//   const history = await redis.get<ChatCompletionMessageParam[]>(`chat:${sessionId}`);
//   return history || [];
// }

// // 保存历史记录
// async function saveMessageHistory(sessionId: string, messages: ChatCompletionMessageParam[]) {
//   await redis.set(`chat:${sessionId}`, messages);
//   // 设置过期时间，比如 24 小时
//   await redis.expire(`chat:${sessionId}`, 24 * 60 * 60);
// }

import OpenAI from "openai";
import { ApiResponseHandler } from "@/lib/api/response";

const openai = new OpenAI({
  apiKey: 'd4799812-c303-4de4-bcd2-520346ce1fc0',
  baseURL: "https://ark.cn-beijing.volces.com/api/v3",
  dangerouslyAllowBrowser: true,
});

import { ChatCompletionMessageParam } from "openai/resources";

// 存储会话历史
let messageHistory: ChatCompletionMessageParam[] = [];

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { messages, reset, sessionId } = body;

    if (!messages ) {
      return ApiResponseHandler.error("消息或会话ID不能为空", 400);
    }

    // 获取或重置历史记录(redis解决方案)
    // let messageHistory = reset ? [] : await getMessageHistory(sessionId);
    messageHistory = [...messageHistory, ...messages];

    const stream = await openai.chat.completions.create({
      model: "deepseek-v3-241226",
      messages: messageHistory,
      temperature: 0.7,
      max_tokens: 2000,
      stream: true,
    });

    const encoder = new TextEncoder();
    const readable = new ReadableStream({
      async start(controller) {
        let assistantMessage = "";
        for await (const chunk of stream) {
          const content = chunk.choices[0]?.delta?.content || "";
          assistantMessage += content;
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify({ content })}\n\n`)
          );
        }
        // 将助手的完整回复添加到历史记录
        messageHistory.push({ role: "assistant", content: assistantMessage });
        controller.close();
      },
    });
console.log(messageHistory)
    return new Response(readable, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    });
    // 在流式响应完成后保存历史记录(redis解决方案)
    // await saveMessageHistory(sessionId, messageHistory);
  } catch (error) {
    console.error("OpenAI API 错误:", error);
    return ApiResponseHandler.error("处理请求时发生错误");
  }
}
