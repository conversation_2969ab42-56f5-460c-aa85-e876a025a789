# 搜索API使用说明

## 概述

这个API支持根据type和keyword查询不同类型的金融数据，包括ETF、股票、指数、外汇和加密货币。支持传统分页和游标分页两种模式。

## 支持的数据类型

- `etf` - ETF数据
- `stock` - 股票数据  
- `index` - 指数数据
- `forex` - 外汇数据
- `crypto` - 加密货币数据

## API端点

### GET /api/kline/search

#### 传统分页模式

```bash
GET /api/kline/search?keyword=茅台&type=stock&limit=10
```

**参数说明：**
- `keyword` (必需): 搜索关键词
- `type` (可选): 数据类型，不指定则搜索所有类型
- `limit` (可选): 返回数量限制，默认20

**响应示例：**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "code": "600519",
        "name": "贵州茅台",
        "type": "stock",
        "region": "CN",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z"
      }
    ],
    "total": 1,
    "type": "stock",
    "keyword": "茅台",
    "pagination": {
      "type": "traditional",
      "limit": 10
    }
  }
}
```

#### 游标分页模式

```bash
GET /api/kline/search?keyword=茅台&type=stock&limit=10&useCursor=true&sortField=id&sortOrder=ASC
```

**参数说明：**
- `useCursor=true` (必需): 启用游标分页
- `cursor` (可选): 游标值，用于获取下一页数据
- `sortField` (可选): 排序字段，支持 id, code, name, created_at，默认id
- `sortOrder` (可选): 排序方向，ASC或DESC，默认ASC

**首次请求响应示例：**
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "code": "600519",
        "name": "贵州茅台",
        "type": "stock",
        "region": "CN",
        "created_at": "2023-01-01T00:00:00.000Z",
        "updated_at": "2023-01-01T00:00:00.000Z"
      }
    ],
    "nextCursor": "10",
    "hasNextPage": true,
    "type": "stock",
    "keyword": "茅台",
    "pagination": {
      "type": "cursor",
      "limit": 10,
      "cursor": null,
      "sortField": "id",
      "sortOrder": "ASC"
    }
  }
}
```

**获取下一页：**
```bash
GET /api/kline/search?keyword=茅台&type=stock&limit=10&useCursor=true&cursor=10&sortField=id&sortOrder=ASC
```

### POST /api/kline/search

获取热门数据，不需要keyword参数。

```bash
POST /api/kline/search
Content-Type: application/json

{
  "type": "stock",
  "limit": 10
}
```

## 游标分页优势

1. **性能更好**: 避免了OFFSET的性能问题，特别是在大数据集中
2. **一致性**: 在数据变化时仍能保持分页的一致性
3. **实时性**: 适合实时数据流的场景

## 使用建议

- **小数据集**: 使用传统分页模式
- **大数据集**: 使用游标分页模式
- **实时数据**: 推荐使用游标分页
- **需要跳页**: 使用传统分页模式

## 错误处理

API会返回详细的错误信息：

```json
{
  "success": false,
  "message": "搜索关键词不能为空",
  "code": 400
}
```

## 扩展性

系统设计具有良好的扩展性：

1. **新数据类型**: 在TYPE_TABLE_MAP中添加新的映射关系
2. **新字段**: 在查询方法中添加新的字段选择
3. **新表结构**: 修改repository中的SQL查询即可

## 数据库表映射

当前使用`markets`表，未来可以切换到独立的数据表：

```typescript
const TYPE_TABLE_MAP = {
  'etf': 'etf_data',
  'stock': 'stock_data', 
  'index': 'index_data',
  'forex': 'forex_data',
  'crypto': 'crypto_data'
}
```
