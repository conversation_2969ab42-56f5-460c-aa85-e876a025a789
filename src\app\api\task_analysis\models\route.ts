import { NextResponse } from 'next/server';
import { ApiResponseHandler } from '@/lib/api/response';
import { TaskAnalysisRepository } from '../repositories';

export async function GET() {
  try {
    const models = await TaskAnalysisRepository.getModelOptions();
    return ApiResponseHandler.success(models);
  } catch (error) {
    console.error('获取分析模型选项失败:', error);
    return ApiResponseHandler.error('获取分析模型选项失败');
  }
}