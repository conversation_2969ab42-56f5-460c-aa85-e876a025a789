"use client";

import React from "react";
import { Col, Form, Input, Radio, Select } from "antd";
import classNames from "classnames";

type ElseFilterProps = {
  renderArray?: Array<{
    props: {
      type: string;
      [key: string]: any;
    };
    formProps?: Record<string, any>;
  }>;
  span?: number;
};

export const ElseFilter: React.FC<ElseFilterProps> = ({
  renderArray,
  span = 6,
}) => {
  return renderArray?.map(({ props, formProps }, index) => {
    const { type, ...componentProps } = props;
    const { label, className, ...restFormProps } = formProps || {};
    let Component;

    switch (type) {
      case "input":
        Component = Input;
        break;
      case "select":
        Component = Select;
        break;
      case "radio":
        Component = Radio.Group;
        break;
      default:
        Component = Input;
    }

    return (
      <Col
        span={span}
        style={{
            paddingTop:3
        }}
        key={index}
        className="dark:bg-dark-bg-300 rounded-lg border dark:border-dark-accent-100"
      >
        <Form.Item
          className={classNames("!mb-0", className)}
          colon={false}
          {...restFormProps}
          label={
            <span className="text-sm leading-10 font-bold mr-2 dark:text-dark-text-100">
              {label}
            </span>
          }
        >
          <Component {...componentProps} />
        </Form.Item>
      </Col>
    );
  });
};
