import { NextResponse } from "next/server";
import { StrategyService } from "../services/StrategyService";
import { ApiResponseHandler } from "@/lib/api/response";

export async function POST(request: Request) {
  try {
    const query = await request.json();
    const { strategy_name,entry_conditions} = query;
    if (!strategy_name || !entry_conditions) {
      return ApiResponseHandler.error("缺少必填条件", 400); 
    }
    const dateRanges = await StrategyService.getDateRanges(strategy_name,entry_conditions);
    return ApiResponseHandler.success(dateRanges);
  } catch (error) {
    return ApiResponseHandler.error(
      error instanceof Error ? error.message : "Internal Server Error",
      500
    );
  }
}
