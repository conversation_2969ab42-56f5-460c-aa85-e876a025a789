import { NextResponse } from "next/server";
import { strategyConfigsRepository } from "./repositories/strategyConfigsRepositories";
import { ApiResponseHandler } from "@/lib/api/response";

export async function GET(req: Request) {
  try {
    const data = await strategyConfigsRepository.getAllStrategyConfigs();

    return ApiResponseHandler.success({
      data: data,
    });
  } catch (error) {
    console.error("获取策略配置失败:", error);
    return ApiResponseHandler.error("查询失败: " + error, 500);
  }
}
