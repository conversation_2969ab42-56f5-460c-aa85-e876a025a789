import { OverViewTabProps } from "@/app/(commont)/strategy_stats/details/[id]/components/OverViewTab";

export interface StrategyTradeDetail {
  id: number;
  test_batch_id: string;
  summary_id: number;
  stock_code: string;
  strategy_name: string;
  trade_id: number;
  entry_date: string;
  entry_time: string;
  exit_date: string;
  exit_time: string;
  holding_days: number;
  entry_price: string;
  exit_price: string;
  quantity: number;
  gross_pnl: string;
  entry_commission: string;
  exit_commission: string;
  total_commission: string;
  net_pnl: string;
  return_pct?: any;
  trade_status: string;
  exit_reason?: any;
  exit_order_type?: any;
  entry_bb_upper?: any;
  entry_bb_middle?: any;
  entry_bb_lower?: any;
  entry_bb_position?: any;
  strategy_params: Strategyparams;
  technical_indicators: Technicalindicators;
  created_at: string;
  updated_at: string;
}

interface Technicalindicators {
  exit: Exit;
  entry: Entry;
}

interface Entry {
  ad: Ad;
  adx: Ad;
  atr: Ad;
  cci: Ad;
  mom: Ad;
  obv: number;
  roc: Ad;
  rsi: Ad;
  sar: number;
  trix: Ad;
  adosc: Ad;
  ema_5: Ad;
  rsi_6: Ad;
  sma_5: Ad;
  willr: Ad;
  adx_25: Ad;
  atr_10: Ad;
  atr_21: Ad;
  cci_20: Ad;
  ema_10: Ad;
  ema_20: Ad;
  ema_50: Ad;
  mom_14: Ad;
  roc_14: Ad;
  rsi_21: Ad;
  sma_10: number;
  sma_20: Ad;
  sma_50: Ad;
  stddev: Ad;
  trange: number;
  volume: number;
  atr_pct: Ad;
  plus_di: Ad;
  stoch_d: Ad;
  stoch_k: Ad;
  bb_lower: Ad;
  bb_upper: Ad;
  bb_width: Ad;
  minus_di: Ad;
  stochf_d: Ad;
  stochf_k: Ad;
  bb_middle: number;
  low_price: number;
  macd_line: Ad;
  stddev_10: Ad;
  vol_ratio: Ad;
  vol_sma_5: number;
  high_price: number;
  open_price: number;
  vol_sma_10: number;
  vol_sma_20: number;
  bb_position: Ad;
  close_price: number;
  macd_signal: Ad;
  bb_width_pct: Ad;
  ichimoku_kijun: Ad;
  macd_histogram: Ad;
  ichimoku_tenkan: Ad;
  technical_score: number;
  candle_body_ratio: Ad;
  lower_shadow_ratio: Ad;
  price_position_20d: Ad;
  upper_shadow_ratio: Ad;
  daily_volatility_pct: Ad;
  volatility_atr_ratio: Ad;
}

interface Exit {
  ad: Ad;
  adx: Ad;
  atr: Ad;
  cci: Ad;
  mom: Ad;
  obv: number;
  roc: Ad;
  rsi: Ad;
  sar: Ad;
  trix: Ad;
  adosc: Ad;
  ema_5: Ad;
  rsi_6: Ad;
  sma_5: number;
  willr: Ad;
  adx_25: Ad;
  atr_10: Ad;
  atr_21: Ad;
  cci_20: Ad;
  ema_10: Ad;
  ema_20: Ad;
  ema_50: Ad;
  mom_14: Ad;
  roc_14: Ad;
  rsi_21: Ad;
  sma_10: Ad;
  sma_20: Ad;
  sma_50: Ad;
  stddev: Ad;
  trange: Ad;
  volume: number;
  atr_pct: Ad;
  plus_di: Ad;
  stoch_d: Ad;
  stoch_k: Ad;
  bb_lower: Ad;
  bb_upper: Ad;
  bb_width: Ad;
  minus_di: Ad;
  stochf_d: Ad;
  stochf_k: Ad;
  bb_middle: number;
  low_price: number;
  macd_line: Ad;
  stddev_10: Ad;
  vol_ratio: Ad;
  vol_sma_5: number;
  high_price: number;
  open_price: number;
  vol_sma_10: number;
  vol_sma_20: number;
  bb_position: Ad;
  close_price: number;
  macd_signal: Ad;
  bb_width_pct: Ad;
  ichimoku_kijun: number;
  macd_histogram: Ad;
  ichimoku_tenkan: number;
  technical_score: Ad;
  candle_body_ratio: Ad;
  lower_shadow_ratio: Ad;
  price_position_20d: Ad;
  upper_shadow_ratio: Ad;
  daily_volatility_pct: Ad;
  volatility_atr_ratio: Ad;
}

interface Ad {
  s: number;
  e: number;
  c: number[];
}

interface Strategyparams {}

export interface TradeDetailsQueryParams {
  summary_id?: number;
  test_batch_id?: string;
  stock_code?: string;
  strategy_name?: string;
  trade_status?: "open" | "closed";
  page?: number;
  pageSize?: number;
  sortField?: string;
  sortOrder?: "asc" | "desc";
}

export interface TradeDetailsResponse {
  data: StrategyTradeDetail[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface OverViewTabResponse {
  data: OverViewTabProps[];
}
