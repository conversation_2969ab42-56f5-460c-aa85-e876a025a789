import { NextResponse } from 'next/server';
import { ApiResponseHandler } from '@/lib/api/response';
import { TaskAnalysisRepository } from './repositories';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const taskId = parseInt(searchParams.get('task_id') || '0');
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    
    if (!taskId) {
      return ApiResponseHandler.error('task_id参数必填', 400);
    }
    
    const models = await TaskAnalysisRepository.getTrainingModels(taskId, page, pageSize);
    return ApiResponseHandler.success(models);
  } catch (error) {
    console.error('获取训练模型数据失败:', error);
    return ApiResponseHandler.error('获取训练模型数据失败');
  }
}