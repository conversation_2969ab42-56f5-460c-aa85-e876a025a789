import { ApiResponseHand<PERSON> } from "@/lib/api/response";

export async function GET(request: Request) {

  try {
    // 获取URL参数
    const { searchParams } = new URL(request.url);
    const stockCode = searchParams.get("stock_code");
    const backtest_id = searchParams.get("backtest_id");

    // 构建API请求URL
    const apiUrl = new URL("http://akshare.mk8s.cn/api/stock/indicators");
    apiUrl.searchParams.set("stock_code", stockCode || "");
    apiUrl.searchParams.set("backtest_id", backtest_id || "");
    const response = await fetch(apiUrl.toString());
    
    // 检查响应状态
    if (!response.ok) {
      const errorText = await response.text();
      return ApiResponseHandler.error(
        `HTTP error! status: ${response.status} ${errorText} `
      );
    }

    // 获取文本内容并尝试处理
    const text = await response.text();
    try {
      // 清理可能的无效字符
      const cleanText = text.replace(/\n/g, "").replace(/NaN/g, "null");
      const json = JSON.parse(cleanText);
      return ApiResponseHandler.success(json.data);
    } catch (parseError) {
      console.error("JSON Parse Error:", parseError);
      console.error("Raw response:", text.substring(0, 200) + "...");
      return ApiResponseHandler.error("Invalid JSON response from server"+ parseError +text);
    }
  } catch (error) {
    console.error("Error fetching stock data:", error);
    return ApiResponseHandler.error("Failed to fetch stock data" + error);
  }
}
