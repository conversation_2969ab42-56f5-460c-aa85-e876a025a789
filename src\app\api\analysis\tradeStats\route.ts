import { NextResponse } from "next/server";
import { StrategyRepository } from "../repository/StrategyRepository";
import { ApiResponseHandler } from "@/lib/api/response";

export async function POST(request: Request) {
  let stats;
  try {
    const { backtest_id, selectedSection } = await request.json();
    if (!backtest_id) {
      return ApiResponseHandler.error(
        "Missing required parameter: backtest_id"
      );
    }
    if (!selectedSection) {
      stats = await StrategyRepository.getTradeStatsAllSections(backtest_id);
    } else {
      stats = await StrategyRepository.getTradeStatsSpecifiedSection(
        backtest_id,
        selectedSection
      );
    }

    return ApiResponseHandler.success(stats);
  } catch (error) {
    return ApiResponseHandler.error(
      error instanceof Error ? error.message : "Internal Server Error"
    );
  }
}
