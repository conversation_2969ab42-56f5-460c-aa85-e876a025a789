import { ApiResponseHandler } from "@/lib/api/response";
import { getAllIndexData } from "./repositories";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = Number(searchParams.get("id"));
  const page = Number(searchParams.get("page")) || 1;
  const pageSize = Number(searchParams.get("pageSize")) || 10;
  const moreSearch = searchParams.get("moreSearch") || "";
  const period = Number(searchParams.get("period")) || 5;

  if(![5,60,1440].includes(period)) {
    return ApiResponseHandler.error('时间间隔不正确', 400);
  }

  if (!id) {
    return ApiResponseHandler.error("Missing required parameter: id", 400);
  }
  try {
    const parsedMoreSearch = moreSearch ? JSON.parse(moreSearch) : undefined;

    const response = await getAllIndexData(
      id,
      page,
      pageSize,
      parsedMoreSearch,
      period
    );
    return ApiResponseHandler.success({
      ...response,
      page,
      pageSize,
    });
  } catch (error) {
    console.error("获取特征详情失败:", error);
    return ApiResponseHandler.error("Internal Server Error", 500);
  }
}
