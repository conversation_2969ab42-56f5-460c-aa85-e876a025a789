import React from 'react';

interface FormLabelProps {
  /**
   * 标签文本内容
   */
  children: React.ReactNode;
  /**
   * 是否必填，显示红色星号
   */
  required?: boolean;
  /**
   * 自定义类名
   */
  className?: string;
  /**
   * 标签大小
   */
  size?: 'small' | 'medium' | 'large';
  /**
   * 标签颜色主题
   */
  variant?: 'default' | 'primary' | 'secondary';
}

/**
 * 通用表单标签组件
 * 用于表单字段的标签显示，支持暗色主题
 */
const FormLabel: React.FC<FormLabelProps> = ({
  children,
  required = false,
  className = '',
  size = 'medium',
  variant = 'default',
}) => {
  // 根据大小设置样式
  const sizeClasses = {
    small: 'text-xs',
    medium: 'text-sm',
    large: 'text-base',
  };

  // 根据主题设置样式
  const variantClasses = {
    default: 'dark:text-dark-text-100',
    primary: 'text-blue-600 dark:text-blue-400',
    secondary: 'text-gray-600 dark:text-gray-400',
  };

  const baseClasses = 'font-bold mr-2';
  const combinedClasses = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`;

  return (
    <span className={combinedClasses}>
      {required && <span className="text-red-500 mr-1">*</span>}
      {children}
    </span>
  );
};

export default FormLabel;
