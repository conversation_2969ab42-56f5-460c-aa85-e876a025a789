import { NextRequest } from "next/server";
import { ApiResponseHandler } from "@/lib/api/response";
import {
  executeSimpleSearch,
  type SearchType,
  type SimpleSearchParams,
} from "./repository";

/**
 * 搜索API - 根据type和keyword查询不同的数据表
 * 支持的类型：etf, stock, index, forex, crypto
 * 返回前200条数据，支持搜索和排序
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const keyword = searchParams.get("keyword");
    const type = searchParams.get("type") as SearchType | undefined;
    const sortOrder =
      (searchParams.get("sortOrder") as "ASC" | "DESC") || "ASC";

    // 验证type参数
    const validTypes = ["etf", "stock", "index", "forex", "crypto"];
    if (type && !validTypes.includes(type)) {
      return ApiResponseHandler.error(
        `无效的类型参数，支持的类型：${validTypes.join(", ")}`,
        400
      );
    }

    // 简单搜索参数
    const searchParams_: SimpleSearchParams = {
      keyword: keyword || undefined,
      sortField: "created_at",
      sortOrder,
    };

    const result = await executeSimpleSearch(type, searchParams_);

    return ApiResponseHandler.success({
      data: result.data,
      total: result.total,
      type: type || "all",
      keyword: keyword,
      sortOrder,
    });
  } catch (error) {
    console.error("搜索失败:", error);
    return ApiResponseHandler.error("服务器内部错误");
  }
}
