// 定义条件数组（与之前一致）
const boll_crossover_conditions = ['K线上穿布林带中轨', '上穿且当前收盘价在中轨之上', '上穿且前一天收盘价在中轨之下', '上穿且当天开盘价在中轨之下'];
const rsi_conditions = ['RSI大于50，上升趋势', 'RSI在中性区域(40-60)', '短周期RSI大于长周期RSI', 'RSI上升'];
const macd_conditions = ['MACD大于0', 'MACD大于信号线', 'MACD上升', 'MACD柱状图增加'];
const mfi_conditions = ['MFI大于50，资金流入', 'MFI在中性区域(40-80)', '短周期MFI大于长周期MFI', 'MFI上升'];
const vwap_conditions = ['收盘价大于VWAP', '收盘价上穿VWAP', '收盘价大于VWAP且成交量增加', '收盘价大于VWAP且上涨'];
const ichimoku_conditions = ['收盘价在云层之上', '转换线大于基准线', '延迟线大于对应的收盘价', '转换线和基准线都上升'];
const supertrend_conditions = ['收盘价大于超级趋势上轨', '收盘价大于超级趋势下轨', '收盘价大于短期和长期超级趋势下轨', '收盘价上穿超级趋势下轨'];

/**
 * 将条件字符串翻译成中文描述
 * @param {string} conditionStr 条件字符串，例如："布林带条件1 + 布林带条件3      布林带条件3 + MFI条件1 + VWAP条件0"
 * @returns {string} 翻译后的中文描述
 */
export function translateConditionString(conditionStr:string) {
    // 分割条件组（支持空格、+、或混合分隔符）
    const conditionGroups = conditionStr?.split(/[\s+]+/).filter(group => group.trim() !== '');
    
    let result = [];
    if (!conditionGroups || conditionGroups.length === 0) {
        return '无条件';
    }
    for (const group of conditionGroups) {
        // 匹配 "指标名条件数字" 的模式
        const match = group.match(/^(.+?)条件(\d+)$/);
        if (!match) continue;
        
        const indicator = match[1];
        const idx = parseInt(match[2]);
        
        let conditionDesc = '';
        
        // 根据指标名获取对应的条件数组
        switch (indicator) {
            case '布林带':
                if (idx >= 0 && idx < boll_crossover_conditions.length) {
                    conditionDesc = boll_crossover_conditions[idx];
                }
                break;
            case 'RSI':
                if (idx >= 0 && idx < rsi_conditions.length) {
                    conditionDesc = rsi_conditions[idx];
                }
                break;
            case 'MACD':
                if (idx >= 0 && idx < macd_conditions.length) {
                    conditionDesc = macd_conditions[idx];
                }
                break;
            case 'MFI':
                if (idx >= 0 && idx < mfi_conditions.length) {
                    conditionDesc = mfi_conditions[idx];
                }
                break;
            case 'VWAP':
                if (idx >= 0 && idx < vwap_conditions.length) {
                    conditionDesc = vwap_conditions[idx];
                }
                break;
            case '一目均衡表':
                if (idx >= 0 && idx < ichimoku_conditions.length) {
                    conditionDesc = ichimoku_conditions[idx];
                }
                break;
            case '超级趋势':
                if (idx >= 0 && idx < supertrend_conditions.length) {
                    conditionDesc = supertrend_conditions[idx];
                }
                break;
        }
        
        if (conditionDesc) {
            result.push(conditionDesc);
        }
    }
    
    // 用 "且" 连接所有条件
    return result.join(' 且 ');
}
