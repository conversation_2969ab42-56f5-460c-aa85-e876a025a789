import pool from "@/lib/quantDB";
import { FinancialNews, FinancialNewsQuery } from "@/types/financial-news";

export interface PaginatedResult<T> {
  data: T[];
  total: number;
}

/**
 * 获取财经新闻数据
 */
export async function getFinancialNews(
  query: FinancialNewsQuery
): Promise<PaginatedResult<FinancialNews>> {
  try {
    const { page, pageSize, keyword, sentiment, impact_level, startDate, endDate, sortField, sortOrder, stock_codes,type } = query;

    // 构建查询条件
    let whereClause = "";
    const queryParams: any[] = [];
    const conditions: string[] = [];

    if (keyword) {
      conditions.push("(title LIKE ? OR summary LIKE ?)");
      queryParams.push(`%${keyword}%`, `%${keyword}%`);
    }

    if (sentiment) {
      conditions.push("sentiment = ?");
      queryParams.push(sentiment);
    }

    if (impact_level !== undefined) {
      conditions.push("impact_level = ?");
      queryParams.push(impact_level);
    }

    if (stock_codes) {
      // 支持多个股票代码搜索，用逗号分隔
      const codeArray = stock_codes.split(",").map(code => code.trim()).filter(code => code);
      if (codeArray.length > 0) {
        const codeConditions = codeArray.map(() => "stock_codes LIKE ?");
        conditions.push(`(${codeConditions.join(" OR ")})`);
        codeArray.forEach(code => {
          queryParams.push(`%${code}%`);
        });
      }
    }

    if (startDate) {
      conditions.push("publish_time >= ?");
      queryParams.push(startDate);
    }

    if (endDate) {
      conditions.push("publish_time <= ?");
      queryParams.push(endDate);
    }

    if (type) {
      conditions.push("type = ?");
      queryParams.push(type);
    }

    if (conditions.length > 0) {
      whereClause = " WHERE " + conditions.join(" AND ");
    }

    // 查询总数
    const [countResult] = await pool.query(
      `SELECT COUNT(*) as total FROM global_stock_news${whereClause}`,
      queryParams
    );
    const total = (countResult as any)[0].total;

    // 计算分页
    const offset = (page - 1) * pageSize;

    // 构建排序子句
    const allowedSortFields = ['publish_time', 'created_at', 'impact_level', 'analysis_status'];
    const validSortField = sortField && allowedSortFields.includes(sortField) ? sortField : 'publish_time';
    const allowedSortOrders = ['asc', 'desc'];
    const validSortOrder = sortOrder && typeof sortOrder === 'string' && allowedSortOrders.includes(sortOrder.toLowerCase()) 
      ? sortOrder.toUpperCase() 
      : 'DESC';
    
    // 安全的构建排序子句 - 避免SQL注入
    const orderClause = `ORDER BY ${validSortField} ${validSortOrder}`;
    
    // 查询数据 - 确保分页参数为数字
    const numPageSize = typeof pageSize === 'string' ? parseInt(pageSize, 10) : pageSize;
    const numOffset = typeof offset === 'string' ? parseInt(offset, 10) : offset;
    
    const [rows] = await pool.query(
      `SELECT
      *
      FROM global_stock_news${whereClause}
      ${orderClause}
      LIMIT ? OFFSET ?`,
      [...queryParams, numPageSize, numOffset]
    );

    return {
      data: rows as FinancialNews[],
      total,
    };
  } catch (error) {
    console.error("获取财经新闻数据失败:", error);
    throw error;
  }
}

/**
 * 获取影响级别选项
 */
export async function getImpactLevels(): Promise<number[]> {
  try {
    const [rows] = await pool.query(
      `SELECT DISTINCT impact_level FROM global_stock_news WHERE impact_level IS NOT NULL ORDER BY impact_level`
    );
    return (rows as any[]).map(row => row.impact_level);
  } catch (error) {
    console.error("获取影响级别失败:", error);
    throw error;
  }
}
