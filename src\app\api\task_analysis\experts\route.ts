import { NextResponse } from 'next/server';
import { getExpertsByTask } from './repositories';
import { ApiResponseHandler } from '../../../../lib/api/response';


export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const taskId = searchParams.get('task_id');
  const search = searchParams.get('search');
  const page = searchParams.get('page') || '1';
  const pageSize = searchParams.get('pageSize') || '10';

  if (!taskId) {
    return ApiResponseHandler.error('task_id is required', 400);
  }

  try {
    const experts = await getExpertsByTask(
      parseInt(taskId),
      parseInt(page),
      parseInt(pageSize)
    );
    return ApiResponseHandler.success(experts);
  } catch (error) {
    return ApiResponseHandler.error('Failed to fetch experts', 500);
  }
}