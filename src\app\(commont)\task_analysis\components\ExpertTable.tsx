'use client'

import React from 'react';
import { Button, Space, Table } from 'antd';
import type { TableProps } from 'antd';

export interface Expert {
  id: string;
  name: string;
  specialty: string;
  experience: number;
  rating: number;
}

interface ExpertTableProps extends Omit<TableProps<Expert>, 'columns'> {
  data: Expert[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  onPaginationChange: (page: number, pageSize: number) => void;
}

const ExpertTable: React.FC<ExpertTableProps> = ({ data, pagination, onPaginationChange, ...props }) => {
    const columns = [
        {
          title: '专家ID',
          dataIndex: 'id',
          key: 'id',
          width: 100
        },
        {
          title: '模型',
          dataIndex: 'model_id',
          key: 'model_id',
          render: (modelId) => `模型 ${modelId}` // 可根据实际需求显示模型名称
        },
        {
          title: '核心能力',
          dataIndex: 'core_competencies',
          key: 'core_competencies',
          width: 200,
          ellipsis: true
        },
        {
          title: '分析策略',
          dataIndex: 'analysis_strategy',
          key: 'analysis_strategy',
          width: 200,
          ellipsis: true
        },
        {
          title: '所需数据',
          dataIndex: 'stock_codes',
          key: 'stock_codes',
          render: (json) => JSON.stringify(json) // 显示JSON格式的股票代码
        },
        {
          title: '所需要的指标',
          dataIndex: 'technical_indicators',
          key: 'technical_indicators',
          render: (json) => JSON.stringify(json) // 显示JSON格式的技术指标
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          render: (status) => status === 0 ? '空闲' : '忙碌'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 150,
          render: (_, record) => (
            <Space>
              {/* <Button type="text" onClick={() => handleEdit(record)}>编辑</Button>
              <Button type="text" danger onClick={() => handleDelete(record)}>删除</Button> */}
            </Space>
          ),
        },
      ];

  return (
    <Table
      columns={columns}
      dataSource={data}
      rowKey="id"
      pagination={{
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        onChange: onPaginationChange,
        showSizeChanger: true,
        showQuickJumper: true,
      }}
      {...props}
    />
  );
};

export default ExpertTable;