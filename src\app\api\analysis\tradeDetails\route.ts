import { NextResponse } from 'next/server'
import { StrategyRepository } from '../repository/StrategyRepository'
import { ApiResponseHandler } from '@/lib/api/response'

export async function POST(request: Request) {
  try {
    const { backtest_id, stock_code } = await request.json()
    const details = await StrategyRepository.getTradeDetails(backtest_id, stock_code)
    return ApiResponseHandler.success(details)
  } catch (error) {
    return ApiResponseHandler.error(error instanceof Error ? error.message : 'Internal Server Error')
  }
}