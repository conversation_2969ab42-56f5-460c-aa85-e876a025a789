import pool from "@/lib/quantDB";

export interface FactorQueryParams {
  page: number;
  pageSize: number;
  name?: string;
  type?: string;
  sortField?:
    | "symbol_type"
    | "ic"
    | "mean_ic"
    | "total_samples"
    | "positive_ratio"
    | "ic_std"
    | "ir"
    | "t_stat"
    | "p_value"
    | 'ic_count';
  sortOrder?: "ASC" | "DESC";
}

export class FactorRepository {
  async count(
    params: Pick<FactorQueryParams, "name" | "type">
  ): Promise<number> {
    let sql = `
      SELECT COUNT(*) as total 
      FROM factors f
      LEFT JOIN (
        SELECT 
          fi1.*
        FROM 
          factor_ic fi1
        INNER JOIN (
          SELECT 
            factor_id, 
            MAX(ic) as max_ic
          FROM 
            factor_ic
          GROUP BY 
            factor_id
        ) fi2 ON fi1.factor_id = fi2.factor_id AND fi1.ic = fi2.max_ic
      ) fic ON f.id = fic.factor_id
      WHERE f.status > 1 AND fic.ic is not null
    `;
    const values = [];

    if (params.name) {
      sql += " AND f.name LIKE ?";
      values.push(`%${params.name}%`);
    }
    if (params.type) {
      sql += " AND f.type = ?";
      values.push(params.type);
    }

    const [countResult] = await pool.query(sql, values);
    return (countResult as any)[0].total;
  }

  async findAll(params: FactorQueryParams) {
    let sql = `
      SELECT 
        f.*,
        fic.id as fic_id,
        fic.factor_id,
        fic.ic,
        fic.mean_ic,
        fic.ic_std,
        fic.ir,
        fic.t_stat,
        fic.p_value,
        fic.total_samples,
        fic.positive_ratio,
        fic.start_date,
        fic.end_date,
        COUNT(fic_all.id) AS ic_count
      FROM 
        factors f
      LEFT JOIN (
        SELECT 
          fi1.*
        FROM 
          factor_ic fi1
        INNER JOIN (
          SELECT 
            factor_id, 
            MAX(ic) as max_ic
          FROM 
            factor_ic
          GROUP BY 
            factor_id
        ) fi2 ON fi1.factor_id = fi2.factor_id AND fi1.ic = fi2.max_ic
      ) fic ON f.id = fic.factor_id
      LEFT JOIN factor_ic fic_all ON f.id = fic_all.factor_id
      WHERE 
        f.status > 1 
        AND fic.ic IS NOT NULL
    `;
    const values = [];

    if (params.name) {
      sql += " AND f.name LIKE ?";
      values.push(`%${params.name}%`);
    }
    if (params.type) {
      sql += " AND f.type = ?";
      values.push(params.type);
    }

    sql += ` GROUP BY 
      f.id,
      fic.id,
      fic.factor_id,
      fic.ic,
      fic.mean_ic,
      fic.ic_std,
      fic.ir,
      fic.t_stat,
      fic.p_value,
      fic.total_samples,
      fic.positive_ratio,
      fic.start_date,
      fic.end_date`;

    // 添加排序
    if (params.sortField && params.sortOrder) {
      if (params.sortField === 'ic_count') {
        sql += ` ORDER BY ic_count ${params.sortOrder}`;
      } else {
        sql += ` ORDER BY fic.${params.sortField} ${params.sortOrder}`;
      }
    } else {
      sql += " ORDER BY f.id";
    }

    sql += " LIMIT ? OFFSET ?";
    values.push(params.pageSize, (params.page - 1) * params.pageSize);

    const [rows] = await pool.query(sql, values);
    return rows;
  }

  async getFactorTypes(): Promise<{ type: string; num: number }[]> {
    const sql = `
      SELECT type, COUNT(*) as num 
      FROM factors 
      WHERE status > 1 
      GROUP BY type
    `;

    const [rows] = await pool.query(sql);
    return rows as { type: string; num: number }[];
  }
  
}

export const factorRepository = new FactorRepository();
