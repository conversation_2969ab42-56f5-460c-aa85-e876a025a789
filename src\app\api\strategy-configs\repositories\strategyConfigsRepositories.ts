import pool from "@/lib/quantDB";
import type { StrategyConfig } from "@/types/strategy-config";

export interface StrategyConfigsRepository {
  getAllStrategyConfigs(): Promise<StrategyConfig[]>;
}

export class StrategyConfigsRepository implements StrategyConfigsRepository {
  async getAllStrategyConfigs(): Promise<StrategyConfig[]> {
    try {
      const [rows] = await pool.query(`SELECT * FROM strategy_configs`);
      return rows as StrategyConfig[];
    } catch (error) {
      console.error("获取策略配置数据失败:", error);
      throw error;
    }
  }
}

export const strategyConfigsRepository = new StrategyConfigsRepository();
