import pool from "@/lib/quantDB";
import { ApiResponseHandler } from "@/lib/api/response";
import { strategyStatsRepository } from "../repositories/strategyStatsRepositories";

export async function PUT(request: Request) {
  try {
    const { id, star } = await request.json();

    const result = await strategyStatsRepository.updateStar(id, star);
    console.log("更新结果:", result);
    return ApiResponseHandler.success({
      code: 200,
      message: "更新成功",
      data: result
    });
  } catch (error) {
    console.error("更新星标失败:", error);
    return ApiResponseHandler.error("更新星标失败", 500);
 
  }
}
