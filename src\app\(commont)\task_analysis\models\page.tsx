"use client";

import React, { useState, useEffect, ReactNode } from "react";
import { useSearchParams } from "next/navigation";
import { Table, Tag, Button, Modal, message, Space } from "antd";
import type { ColumnsType } from "antd/es/table";
import {
  getModelKline,
  getTrainedModals,
  MonthlyStats,
  TrainedModelType,
} from "../services/taskAnalysisService";
import moment from "moment";
import { MODEL_ALGORITHM, TARGET_TYPE } from "@/constants";
import { useRouter } from "next/navigation";
import CheckableTag from "antd/es/tag/CheckableTag";
import request from "@/utils/request";
import { set } from "lodash";

export default function ModelDetailPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const taskId = searchParams.get("task_id");
  if (!taskId) {
    message.error("缺少任务ID");
    router.push("/task_analysis");
  }

  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<TrainedModelType | null>(
    null
  );
  const [data, setData] = useState<TrainedModelType[]>([]);

  const [pagination, setPagination] = useState<{
    current: number;
    pageSize: number;
    total?: number;
  }>({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const columns: ColumnsType<TrainedModelType> = [
    {
      title: "模型ID",
      dataIndex: "id",
      key: "id",
      width: 80,
    },
    {
      title: "目标类型",
      dataIndex: "target_type",
      key: "target_type",
      render: (type: 0 | 1 | 2 | 3) => <Tag>{TARGET_TYPE[type]}</Tag>,
    },
    {
      title: "预测次数",
      dataIndex: "forecast_count",
      key: "forecast_count",
    },
    {
      title: "算法",
      dataIndex: "model_algorithm",
      render: (text) => MODEL_ALGORITHM[text as keyof typeof MODEL_ALGORITHM],
    },
    {
      title: "特征选择函数",
      dataIndex: "feature_select_method",
    },
    {
      title: "周期(分）",
      dataIndex: "period",
    },
    {
      title: "训练结果",
      dataIndex: "train_count",
      key: "train_count",
      children: [
        {
          title: "成功次数",
          dataIndex: "success_count",
          align: "center",
          key: "success_count",
          render: (text, record) => {
            return (
              <>
                <span className="text-green-500">{text}</span>
              </>
            );
          },
        },
        {
          title: "失败次数",
          dataIndex: "failure_count",
          align: "center",
          key: "failure_count",
          render: (text, record) => {
            return <span className="text-red-500">{text} </span>;
          },
        },
        {
          title: "成功率",
          dataIndex: "success_rate",
          align: "center",
          key: "success_rate",
          render: (text, record) => {
            return (
              <span className={text > 70 ? "text-green-500" : "text-red-500"}>
                {text}%
              </span>
            );
          },
        },
      ],
    },
    {
      title: "创建时间",
      dataIndex: "create_time",
      key: "create_time",
      render: (text, record) => (
        <>{moment.utc(text).format("YYYY-MM-DD HH:mm:ss")}</>
      ),
    },
    {
      title: "操作",
      key: "action",
      align: "center",
      onCell: () => ({
        onClick: (e) => {
          e.stopPropagation(); // 阻止事件冒泡
          setCurrentRecord(null);
          setModalVisible(false);
        },
        style: {
          cursor: "auto",
        },
        title: " "
      }),
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={(e) => {
              e.stopPropagation(); // 阻止事件冒泡
              setCurrentRecord(record);
              setModalVisible(true);
            }}
          >
            月度统计详情
          </Button>
          <Button
            type="link"
            onClick={(e) => {
              e.stopPropagation(); // 阻止事件冒泡
              window.open(
                `/task_analysis/models/kline?model_id=${record.id}`,
                "_blank"
              );
            }}
          >
            查看预测K线图
          </Button>
        </Space>
      ),
    },
  ];

  const fetchData = async () => {
    if (!taskId) return;
    try {
      setLoading(true);
      const { data, pagination: backPagination } = await getTrainedModals(
        Number(taskId),
        pagination.current,
        pagination.pageSize
      );
      setData(data);
      setPagination({
        total: backPagination.total,
        pageSize: backPagination.pageSize,
        current: backPagination.current,
      });
    } catch (error) {
      message.error("获取模型数据失败");
      console.error("获取模型数据失败:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (taskId) {
      fetchData();
    }
  }, [taskId, pagination.current, pagination.pageSize]);

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">
        模型训练列表
        <span className="text-base font-bold mb-4"> (任务ID：{taskId})</span>
      </h1>

      <Modal
        title="月度统计详情"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Table
          dataSource={currentRecord?.monthly_stats}
          columns={[
            {
              title: "月份",
              dataIndex: "month_period",
              key: "month_period",
            },
            {
              title: "总预测次数",
              dataIndex: "total_count",
              key: "total_count",
            },
            {
              title: "成功次数",
              dataIndex: "success_count",
              key: "success_count",
            },
            {
              title: "成功率(%)",
              dataIndex: "success_rate_percent",
              key: "success_rate_percent",
              render: (text) => <span>{text}%</span>,
            },
          ]}
          pagination={false}
        />
      </Modal>

      <Table
        columns={columns}
        dataSource={data}
        bordered
        rowKey="id"
        loading={loading}
        onRow={(record) => ({
          onClick: () => {
            window.open(
              `/task_analysis/models/forecast?model_id=${record.id}`,
              "_blank"
            );
          },
          style: {
            cursor: "pointer",
          },
          title: "查看预测详细 ",
        })}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          onChange: (page, pageSize) => {
            setPagination({
              current: page,
              pageSize,
            });
          },
        }}
      />
    </div>
  );
}
