import { NextRequest } from "next/server";
import { ApiResponseHandler } from "@/lib/api/response";
import { StrategyTradeDetailsService } from "../../services";
import { number } from "echarts/types/src/echarts.all.js";
import moment from "moment";

interface RouteContext {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, context: RouteContext) {
  try {
    const id = Number(context.params.id);
    if (!Number.isFinite(id)) {
      return ApiResponseHandler.error("无效的ID参数", 400);
    }

    const tradeDetails = await StrategyTradeDetailsService.getTradeDataForChart(
      id
    );
    if (!tradeDetails?.length) {
      return ApiResponseHandler.error("未找到指定的交易详情", 404);
    }

    const initialValue = 300 * 10_000; // 使用数字分隔符提高可读性
    let cumulativeSum = initialValue;

    const result = tradeDetails.map((item) => {
      const netPnl = Number(item.net_pnl ?? 0); // 默认值处理
      cumulativeSum += netPnl;
      return {
        ...item,
        entry_date: moment(item.entry_date).format("YYYY-MM-DD"),
        entry_time: moment(item.entry_time).format("YYYY-MM-DD"),
        exit_date: moment(item.exit_date).format("YYYY-MM-DD"),
        exit_time: moment(item.exit_time).format("YYYY-MM-DD"),

        cumulativeValue: parseFloat(cumulativeSum.toFixed(2)), // 保留两位小数
      }; // 更清晰的字段名
    });

    return ApiResponseHandler.success(
      {
        data: result,
      },
      "查询成功"
    );
  } catch (error) {
    console.error(`查询策略交易详情失败 (ID: ${context.params.id}):`, error);
    const errorMessage = error instanceof Error ? error.message : "查询失败";
    return ApiResponseHandler.error(errorMessage, 500);
  }
}
