import { ApiResponseHandler } from "@/lib/api/response";

export async function GET(
  request: Request,
  { params }: { params: { stock_code: string } }
) {
  try {
    let { stock_code } = params;

    if (!stock_code) {
      return ApiResponseHandler.error("股票代码不能为空", 400);
    }

    if(stock_code.length === 4) {
      stock_code = `0${stock_code}`;
    }

    // 构建外部API请求URL
    const apiUrl = new URL(
      "http://api.waizaowang.com/doc/getWatchStockTimeKLine"
    );
    apiUrl.searchParams.set("type", stock_code.length === 5 ? "3" : "1");
    apiUrl.searchParams.set("code", stock_code);
    apiUrl.searchParams.set("export", "1");
    apiUrl.searchParams.set("token", "ab96d998a83e00d5ffe028b012c0b405");
    apiUrl.searchParams.set("fields", "price,tdate,zdfd,zded,cjl,cje,hslv,name");

    // 发起请求到外部API
    const response = await fetch(apiUrl.toString(), {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    // 检查响应状态
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`外部API请求失败: ${response.status} ${errorText}`);
      return ApiResponseHandler.error(
        `获取股票数据失败: ${response.status}`,
        500
      );
    }

    // 获取响应数据
    const data = await response.json();

    if (!data.data || data.data.length === 0) {
      return ApiResponseHandler.error("未找到股票数据", 500);
    }

    // 返回标准化响应
    return ApiResponseHandler.success({
      content: data.data[0],
      stock_code,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("获取股票数据失败:", error);
    return ApiResponseHandler.error("获取股票数据失败，请稍后重试", 500);
  }
}

export const dynamic = "force-dynamic";
