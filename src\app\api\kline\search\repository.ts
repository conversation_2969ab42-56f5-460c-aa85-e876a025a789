import pool from "@/lib/quantDB";

// 数据类型到表的映射关系
const TYPE_TABLE_MAP = {
  etf: "etf_data",
  stock: "stock_data",
  index: "index_data",
  forex: "forex_data",
  crypto: "crypto_data",
} as const;

type SearchType = keyof typeof TYPE_TABLE_MAP;

// 基础搜索结果接口
interface BaseSearchResult {
  id?: number;
  ticker: string;
  name: string;
  type: string;
  [key: string]: any; // 允许扩展字段
}

// 简单搜索参数接口
interface SimpleSearchParams {
  keyword?: string;
  sortField?: "created_at" | "id" | "ticker" | "name"; // 排序字段
  sortOrder?: "ASC" | "DESC"; // 排序方向
}

// 简单搜索结果接口
interface SimpleSearchResult<T> {
  data: T[];
  total: number; // 返回的数据总数
}

// ETF数据简单搜索
export async function queryETFData(
  params: SimpleSearchParams
): Promise<SimpleSearchResult<BaseSearchResult>> {
  try {
    const {
      keyword,
      sortField = "created_at",
      sortOrder = "ASC",
    } = params;

    // 验证 sortField 是否为有效的列名
    const validSortFields = ["id", "ticker", "name", "created_at", "ticker"];
    const safeSortField = validSortFields.includes(sortField) ? sortField : "created_at";

    // 验证 sortOrder 是否为有效的排序方向
    const safeSortOrder = sortOrder === "DESC" ? "DESC" : "ASC";

    let sql = `
      SELECT *
      FROM etf_data
      WHERE 1=1
    `;
    const queryParams: any[] = [];

    if (keyword) {
      sql += ` AND (ticker LIKE ? OR name LIKE ?)`;
      const searchPattern = `%${keyword}%`;
      queryParams.push(searchPattern, searchPattern);
    }

    sql += ` ORDER BY ${safeSortField} ${safeSortOrder}`;
    sql += ` LIMIT 200`; // 固定返回前200条

    const [rows] = await pool.execute(sql, queryParams);
    const results = rows as any[];

    return {
      data: results,
      total: results.length,
    };
  } catch (error) {
    console.error("查询ETF数据失败:", error);
    throw error;
  }
}

/**
 * 通用简单搜索方法
 * 根据类型和参数执行相应的搜索查询，返回前200条数据
 */
export async function executeSimpleSearch(
  type: SearchType | undefined,
  params: SimpleSearchParams
): Promise<SimpleSearchResult<BaseSearchResult>> {
  switch (type) {
    case "etf":
      return await queryETFData(params);
    case "stock":
      return await queryStockData(params);
    case "forex":
      return await queryForexData(params);
    case "crypto":
      return await queryCryptoData(params);
    case "index":
      return await queryIndexData(params);
    default:
      // 如果没有指定类型，查询所有类型的数据
      return await queryAllData(params);
  }
}


// 股票数据简单搜索
async function queryStockData(
  params: SimpleSearchParams
): Promise<SimpleSearchResult<BaseSearchResult>> {
  try {
    const {
      keyword,
      sortField = "created_at",
      sortOrder = "ASC",
    } = params;


    // 验证 sortOrder 是否为有效的排序方向
    const safeSortOrder = sortOrder === "DESC" ? "DESC" : "ASC";

    let sql = `
      SELECT *
      FROM stock_data
      WHERE 1=1
    `;
    const queryParams: any[] = [];

    if (keyword) {
      sql += ` AND (ticker LIKE ? OR name LIKE ?)`;
      const searchPattern = `%${keyword}%`;
      queryParams.push(searchPattern, searchPattern);
    }

    sql += ` ORDER BY ${sortField} ${safeSortOrder}`;
    sql += ` LIMIT 200`; // 固定返回前200条

    const [rows] = await pool.execute(sql, queryParams);
    const results = rows as any[];

    return {
      data: results,
      total: results.length,
    };
  } catch (error) {
    console.error("查询股票数据失败:", error);
    throw error;
  }
}

// 指数数据简单搜索
async function queryIndexData(
  params: SimpleSearchParams
): Promise<SimpleSearchResult<BaseSearchResult>> {
  return queryGenericData(params, "index_data");
}

// 外汇数据简单搜索
async function queryForexData(
  params: SimpleSearchParams
): Promise<SimpleSearchResult<BaseSearchResult>> {
  return queryGenericData(params, "forex_data");
}

// 加密货币数据简单搜索
async function queryCryptoData(
  params: SimpleSearchParams
): Promise<SimpleSearchResult<BaseSearchResult>> {
  return queryGenericData(params, "crypto_data");
}

// 通用的简单搜索方法
async function queryGenericData(
  params: SimpleSearchParams,
  tableName: string
): Promise<SimpleSearchResult<BaseSearchResult>> {
  try {
    const {
      keyword,
      sortField = "created_at",
      sortOrder = "ASC",
    } = params;



    let sql = `
      SELECT *
      FROM ${tableName}
      WHERE 1=1
    `;
    const queryParams: any[] = [];

    if (keyword) {
      sql += ` AND (ticker LIKE ? OR name LIKE ?)`;
      const searchPattern = `%${keyword}%`;
      queryParams.push(searchPattern, searchPattern);
    }

    sql += ` ORDER BY ${sortField} ${sortOrder}`;
    sql += ` LIMIT 200`; // 固定返回前200条

    const [rows] = await pool.execute(sql, queryParams);
    const results = rows as any[];

    return {
      data: results,
      total: results.length,
    };
  } catch (error) {
    console.error(`查询${tableName}数据失败:`, error);
    throw error;
  }
}

// 查询所有类型的数据
async function queryAllData(
  params: SimpleSearchParams
): Promise<SimpleSearchResult<BaseSearchResult>> {
  try {
    const results = await Promise.all([
      queryETFData(params),
      queryStockData(params),
      queryIndexData(params),
      queryForexData(params),
      queryCryptoData(params),
    ]);

    // 合并所有结果
    const allData = results.flatMap(result => result.data);

    // 根据排序参数对合并后的数据进行排序
    const { sortField = "created_at", sortOrder = "ASC" } = params;
    allData.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (sortOrder === "DESC") {
        return bValue > aValue ? 1 : -1;
      } else {
        return aValue > bValue ? 1 : -1;
      }
    });

    // 限制返回前200条
    const limitedData = allData.slice(0, 200);

    return {
      data: limitedData,
      total: limitedData.length,
    };
  } catch (error) {
    console.error("查询所有数据失败:", error);
    throw error;
  }
}

export type {
  SearchType,
  BaseSearchResult,
  SimpleSearchParams,
  SimpleSearchResult,
};
