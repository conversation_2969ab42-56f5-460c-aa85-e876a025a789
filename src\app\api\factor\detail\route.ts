import { type NextRequest } from 'next/server';
import { factorRepository } from '@/app/api/factor/repositories/factorDetailsRepositories';
import { ApiResponseHandler } from '@/lib/api/response';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const factorId = searchParams.get('factorID');
    const page = Number(searchParams.get('page')) || 1;
    const pageSize = Number(searchParams.get('pageSize')) || 10;
    const holding_period = Number(searchParams.get('holding_period')) || null;
    const sortField = searchParams.get('sortField') as
    | "symbol_type"
    | "ic"
    | "mean_ic"
    | "total_samples"
    | "positive_ratio"
    | "ic_std"
    | "ir"
    | "t_stat"
    | "p_value"
    | 'ic_count';
    const sortOrder = searchParams.get('sortOrder') as 'ASC' | 'DESC' | undefined;

    if (!factorId) {
      return ApiResponseHandler.error('缺少因子ID参数', 400);
    }

    const [total, rows] = await Promise.all([
      factorRepository.count({ id: Number(factorId) }),
      factorRepository.findById({
        id: Number(factorId),
        page,
        pageSize,
        sortField,
        sortOrder,
        holding_period
      })
    ]);

    if (!rows) {
      return ApiResponseHandler.error('因子不存在', 404);
    }

    return ApiResponseHandler.success({
      data: rows,
      pagination: {
        current: page,
        pageSize,
        total
      }
    });
  } catch (error) {
    console.error('获取因子详情失败:', error);
    return ApiResponseHandler.error('获取因子详情失败');
  }
}