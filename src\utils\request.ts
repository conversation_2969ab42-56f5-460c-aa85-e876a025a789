import axios from 'axios';

interface ApiResponse<T> {
  code: number;
  data?: T;
  message: string;
  success: boolean;
}

const request = axios.create({
  baseURL: '/api',
  timeout: 1000000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const res = response.data as ApiResponse<any>;
    if (!res.success) {
      return Promise.reject(new Error(res.message));
    }
    return res.data;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default request;