import pool from "@/lib/quantDB";
import {
  StrategyTradeDetail,
  TradeDetailsQueryParams,
} from "@/types/strategy-trade-details";

export class StrategyTradeDetailsRepository {
  /**
   * 构建WHERE条件和参数
   */
  private static buildWhereConditions(params: TradeDetailsQueryParams) {
    const whereConditions: string[] = [];
    const queryParams: any[] = [];

    if (params.summary_id) {
      whereConditions.push("summary_id = ?");
      queryParams.push(params.summary_id);
    }

    if (params.test_batch_id) {
      whereConditions.push("test_batch_id = ?");
      queryParams.push(params.test_batch_id);
    }

    if (params.stock_code) {
      whereConditions.push("stock_code LIKE ?");
      queryParams.push(`%${params.stock_code}%`);
    }

    if (params.strategy_name) {
      whereConditions.push("strategy_name = ?");
      queryParams.push(params.strategy_name);
    }

    if (params.trade_status) {
      whereConditions.push("trade_status = ?");
      queryParams.push(params.trade_status);
    }

    return { whereConditions, queryParams };
  }

  /**
   * 获取策略交易详情总数
   */
  static async getCount(params: TradeDetailsQueryParams): Promise<number> {
    const { whereConditions, queryParams } = this.buildWhereConditions(params);

    let countQuery = `SELECT COUNT(*) as total FROM strategy_trade_details`;

    if (whereConditions.length > 0) {
      countQuery += ` WHERE ${whereConditions.join(" AND ")}`;
    }

    const connection = await pool.getConnection();

    try {
      const [countResult] = await connection.execute(countQuery, queryParams);
      return (countResult as any[])[0].total;
    } finally {
      connection.release();
    }
  }

  /**
   * 获取策略交易详情列表
   */
  static async getList(
    params: TradeDetailsQueryParams
  ): Promise<StrategyTradeDetail[]> {
    const { whereConditions, queryParams } = this.buildWhereConditions(params);
    let baseQuery = `
      SELECT 
        id, test_batch_id, summary_id, stock_code, strategy_name, trade_id,
        entry_date, entry_time, exit_date, exit_time, holding_days,
        entry_price, exit_price, quantity, gross_pnl, 
        entry_commission, exit_commission, total_commission, net_pnl, return_pct,
        trade_status, exit_reason, exit_order_type,
        entry_bb_upper, entry_bb_middle, entry_bb_lower, entry_bb_position,
        strategy_params, technical_indicators, created_at, updated_at
      FROM strategy_trade_details
    `;

    if (whereConditions.length > 0) {
      baseQuery += ` WHERE ${whereConditions.join(" AND ")}`;
    }

    // 添加排序
    const validSortFields = [
      "entry_time",
      "exit_time",
      "return_pct",
      "net_pnl",
      "holding_days",
      "created_at",
    ];
    const sortField = validSortFields.includes(params.sortField!)
      ? params.sortField
      : "entry_time";
    baseQuery += ` ORDER BY ${sortField} ${params.sortOrder?.toUpperCase()}`;

    // 添加分页
    const offset = ((params.page || 1) - 1) * (params.pageSize || 20);
    baseQuery += ` LIMIT ? OFFSET ?`;
    queryParams.push(params.pageSize || 20, offset);

    const connection = await pool.getConnection();
    try {
      const [rows] = await connection.query(baseQuery, queryParams);
      return rows as StrategyTradeDetail[];
    } finally {
      connection.release();
    }
  }

  /**
   * 根据ID获取单个策略交易详情
   */
  static async getById(id: number): Promise<StrategyTradeDetail | null> {
    const query = `
      SELECT 
        id, test_batch_id, summary_id, stock_code, strategy_name, trade_id,
        entry_date, entry_time, exit_date, exit_time, holding_days,
        entry_price, exit_price, quantity, gross_pnl, 
        entry_commission, exit_commission, total_commission, net_pnl, return_pct,
        trade_status, exit_reason, exit_order_type,
        entry_bb_upper, entry_bb_middle, entry_bb_lower, entry_bb_position,
        strategy_params, technical_indicators, created_at, updated_at
      FROM strategy_trade_details
      WHERE id = ?
    `;

    const connection = await pool.getConnection();

    try {
      const [rows] = await connection.execute(query, [id]);
      const results = rows as StrategyTradeDetail[];
      return results.length > 0 ? results[0] : null;
    } finally {
      connection.release();
    }
  }

  /**
   * 根据汇总ID获取所有交易详情
   */
  static async getAll(summaryId: number): Promise<StrategyTradeDetail[]> {
    const query = `
      SELECT 
        id, summary_id, stock_code, 
        entry_date, entry_time, exit_date, exit_time, holding_days, 
        ROUND(gross_pnl, 2) as gross_pnl, 
        ROUND(net_pnl, 2) as net_pnl
      FROM strategy_trade_details
      WHERE summary_id = ? 
      ORDER BY entry_time ASC
    `;

    try {
      const connection = await pool.getConnection();
      const [rows] = await connection.execute(query, [summaryId]);
      connection.release();

      return rows as StrategyTradeDetail[];
    } catch (error) {
      console.error(`获取所有交易详情失败 (Summary ID: ${summaryId}):`, error);
      throw new Error("获取所有交易详情失败");
    }
  }
}
