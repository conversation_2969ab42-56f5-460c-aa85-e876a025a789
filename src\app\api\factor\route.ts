import { NextResponse } from 'next/server';
import { type NextRequest } from 'next/server';
import { factorRepository } from '@/app/api/factor/repositories/factorRepository';
import { ApiResponseHandler } from "@/lib/api/response"

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const name = searchParams.get('name') || '';
    const type = searchParams.get('type') || '';
    const sortField = searchParams.get('sortField') as | "symbol_type"
    | "ic"
    | "mean_ic"
    | "total_samples"
    | "positive_ratio"
    | "ic_std"
    | "ir"
    | "t_stat"
    | "p_value"| undefined;
    const sortOrder = searchParams.get('sortOrder') as 'ASC' | 'DESC' | undefined;

    const [total, rows] = await Promise.all([
      factorRepository.count({ 
        name, 
        type
      }),
      factorRepository.findAll({ 
        page, 
        pageSize, 
        name, 
        type,
        sortField,
        sortOrder
      })
    ]);

    return ApiResponseHandler.success({
      data: rows,
      pagination: {
        current: page,
        pageSize,
        total
      }
    })
  } catch (error) {
    console.error('获取因子数据失败:', error)
    return ApiResponseHandler.error('获取数据失败')
  }
}

