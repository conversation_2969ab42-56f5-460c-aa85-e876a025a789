FROM harbor.duomai.com/tool/node:20-alpine-brotliv2 AS base
# 公共变量
ENV NODE_ENV production
ENV NEXT_PUBLIC_APP_TITLE=your_project_name
ENV NEXT_PUBLIC_LOGIN_URL=https://maibo.sk8s.cn/login?step=Login
ENV NEXT_PUBLIC_REGISTER_URL=https://maibo.sk8s.cn/login?step=Register
## 注意别放到.env 里面，脚本要用
## 如果你需要静态资源上传oss请打开，如果只想上传分析文件请移到运行时（node）变量
# ENV NEXT_PUBLIC_BASE_URL=http://cps-api.sk8s.cn/newgate/newcps/backend
# ENV STATIC_DAMON=https://dm-front-page.oss-cn-hangzhou.aliyuncs.com
# ENV REGION=oss-cn-hangzhou
# ENV BUCKET=dm-front-page
# ENV ACCESS_KEY_ID=LTAI5tNLYAfPXNu4gxUKymcj
# ENV ACCESS_KEY_SECRET=******************************

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
# RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
# 如果你需要固定版本
# @Example
# yarn global add pnpm@^7.33.6
ARG NPM_CONFIG_REGISTRY=http://************:4873/

RUN \
  if [ -f yarn.lock ]; then yarn install --production; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then yarn global add pnpm@^9.11.0 && pnpm i --frozen-lockfile --ignore-scripts --production; \
  else echo "Lockfile not found." && exit 1; \
  fi


# Rebuild the source code only when needed
FROM base AS builder
## 环境变量
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
## 构建时（客户端）变量
ARG IS_CI=true
# ARG ASSET_PREFIX=/toptrends
RUN npm run build

FROM base AS runner

WORKDIR /app
COPY ./.docker/nginx.conf /etc/nginx/conf.d/default.conf
COPY ./.docker/nginx_default.conf /etc/nginx/nginx.conf
COPY ./.docker/50x.html /usr/share/nginx/html/50x.html
RUN rm -rf ./.docker/nginx.conf ./.docker/nginx_default.conf
RUN mkdir -p /var/cache/nginx/client_temp
## 运行时（node）变量
# ENV ASSET_PREFIX=/toptrends/snapshot
ARG IS_CI=true
# https://nextjs.org/docs/advanced-features/output-file-tracing#automatically-copying-traced-files
COPY --from=builder /app/public ./public
COPY --from=builder  /app/.next/standalone ./
# COPY --from=builder  /app/scripts/upload-snapshot.js ./upload-snapshot.js
# COPY --from=builder  /app/ecosystem.config.js ./ecosystem.config.js
COPY --from=builder  /app/.next/static ./.next/static

RUN npm install -g \
    pm2@^5.3.1 clinic@^13.0.0 --registry=https://registry.npmmirror.com/ && \
    npm install -g \
    webpack-aliyun-oss@0.5.10 --registry=http://************:4873/

EXPOSE 80

ENV PORT 3000
ENV HOSTNAME 0.0.0.0
ENV KEEP_ALIVE_TIMEOUT 70000

CMD ["sh", "-c", "pm2-runtime start server.js &amp;nginx -g 'daemon off;'"]
