import React, { ReactNode, use } from "react";
import {
  App,
  But<PERSON>,
  message,
  Modal,
  Row,
  Space,
  Table,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import type { ColProps, TableProps } from "antd";
import { AiAnalysisTask } from "@/types/ai-analysis";
import { ColumnsType } from "antd/es/table";
import { Col, FormInstance } from "antd/lib";
import { deleteTaskAnalysisData } from "../services/taskAnalysisService";
import { useRouter } from "next/navigation";
import useModal from "antd/es/modal/useModal";
import moment from "moment";
import classNames from "classnames";
import Image from "next/image";
import TimeRangeDisplay from "./TimeRangeDisplay";
import FeatureDetailModal from "./FeatureDetailModal";
import { FEATURE_TYPE, MODEL_ALGORITHM } from "@/constants";

interface TaskTableProps extends Omit<TableProps<AiAnalysisTask>, "columns"> {
  data: AiAnalysisTask[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  fetchData: () => Promise<void>;
  onPaginationChange: (page: number, pageSize: number) => void;
  form: FormInstance;
  setOpen: (open: boolean) => void;
}

const modalLogoMap = {
  deepseek: "/icon/deepseek.svg",
  qwq: "/icon/qwq.svg",
  model: "/icon/model.svg",
};

const getLogoByModelName = (name: string) => {
  const lowerName = name.toLowerCase();
  for (const [key, value] of Object.entries(modalLogoMap)) {
    if (lowerName.includes(key)) {
      return value;
    }
  }
  return null;
};

const TaskTable: React.FC<TaskTableProps> = ({
  data,
  form,
  setOpen,
  pagination,
  fetchData,
  onPaginationChange,
  ...props
}) => {
  const [modal, contextHolderModal] = useModal();
  const [featureModalVisible, setFeatureModalVisible] = React.useState(false);
  const [currentTaskId, setCurrentTaskId] = React.useState<number>();
  const router = useRouter();

  const columns: ColumnsType<AiAnalysisTask> = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      fixed: "left",
      width: 80,
    },
    {
      title: "分析目标",
      fixed: "left",
      width: 150,
      dataIndex: "target_name",
      key: "target_name",
    },
    {
      title: "使用的数据",
      dataIndex: "market_names",
      key: "market_names",
      render: (names: AiAnalysisTask["market_names"], record) => {
        if (!names) {
          return null;
        }
        return <TagRender names={names} />;
      },
    },
    {
      title: "特征类型",
      dataIndex: "feature_type",
      key: "feature_type",
      render: (type: keyof typeof FEATURE_TYPE) => FEATURE_TYPE[type],
    },
    {
      title: "特征值标准化",
      dataIndex: "feature_normalization",
      key: "feature_normalization",
      render: (featureNormalization) => {
        return (
          <span
            className={featureNormalization ? "text-green-500" : "text-red-500"}
          >
            {featureNormalization ? "是" : "否"}
          </span>
        );
      },
    },
    {
      title: "特征数量",
      dataIndex: "feature_num",
      key: "feature_num ",
      render: (featureNum, record) => {
        return (
          <Tag
            title="点击查看特征详情"
            color="blue"
            onClick={async () => {
              setFeatureModalVisible(true);
              setCurrentTaskId(record.id);
            }}
            style={{ cursor: "pointer" }}
          >
            {featureNum}
          </Tag>
        );
      },
    },

    {
      title: "模型",
      dataIndex: "model_titles",
      key: "model_titles",
      width: 150,
      render: (models: Array<string>) => {
        const renderArray = models.map((name, index) => {
          const logo = getLogoByModelName(name);
          return (
            <div
              key={`model-${index}-${name}`}
              className="flex items-center gap-2"
            >
              {logo && (
                <Image
                  src={logo}
                  alt={name}
                  width={16}
                  height={16}
                  className="rounded-full"
                />
              )}
              <span>{name}</span>
            </div>
          );
        });

        return <TagRender names={renderArray} width={150} />;
      },
    },
    {
      title: "模型算法",
      dataIndex: "model_algorithm",
      key: "model_algorithm",
      render: (algorithm: keyof typeof MODEL_ALGORITHM) =>
        MODEL_ALGORITHM[algorithm],
    },
    {
      title: "训练模型数量",
      dataIndex: "model_num",
      key: "model_num",
      render: (modelNum, record) => {
        return (
          <Tag
            title="点击查看模型详情"
            color="purple"
            onClick={async () => {
              try {
                window.open(
                  `/task_analysis/models?task_id=${record.id}`,
                  "_blank"
                );
              } catch (error) {
                console.error("导航到模型详情页失败:", error);
              }
            }}
            style={{ cursor: "pointer" }}
          >
            {modelNum}
          </Tag>
        );
      },
    },

    // {
    //   title: "📊 指标",
    //   dataIndex: "technical_indicators",
    //   key: "technical_indicators",
    //   render: (indicators: Array<string>) => {
    //     return indicators?.map((indicator, index) => {
    //       return (
    //         <Tag key={`indicator-${index}-${indicator}`} color="blue-inverse">
    //           {indicator}
    //         </Tag>
    //       );
    //     });
    //   },
    // },
    // {
    //   title: "💬 数据分析",
    //   key: "analysis_data",
    //   render: (_, record) => (
    //     <a
    //       className="flex flex-col gap-2  min-h-[48px] dark:text-dark-text-200"
    //       href="/task_analysis/experts"
    //       target="_blank"
    //       title="点击查看数据分析"
    //     >
    //       <div
    //         className={classNames("flex gap-1 items-center p-1 rounded", {
    //           "bg-green-50": record.dataRatio > record.inverseDataRatio,
    //           "bg-red-50": record.dataRatio <= record.inverseDataRatio,
    //         })}
    //       >
    //         <Tooltip title={`正面数据: ${record.dataRatio ?? 0}`}>
    //           <span className="whitespace-nowrap">
    //             👍
    //             <a href="">{record.dataRatio ?? 0}</a>
    //           </span>
    //         </Tooltip>
    //         <Tooltip title={`负面数据: ${record.inverseDataRatio ?? 0}`}>
    //           <span className="whitespace-nowrap">
    //             👎<a href="">{record.inverseDataRatio ?? 0}</a>
    //           </span>
    //         </Tooltip>
    //         <Tooltip title={`未知数据: ${record.unknownDataRatio ?? 0}`}>
    //           <a className="whitespace-nowrap">
    //             ❓{record.unknownDataRatio ?? 0}
    //           </a>
    //         </Tooltip>
    //         <Tooltip
    //           title={`正面数据占比: ${Math.round(
    //             (record.dataRatio /
    //               (record.dataRatio +
    //                 record.inverseDataRatio +
    //                 record.unknownDataRatio)) *
    //               100
    //           )}%`}
    //         >
    //           <span className="ml-2 text-xs">
    //             <a className="whitespace-nowrap">
    //               {Math.round(
    //                 (record.dataRatio /
    //                   (record.dataRatio +
    //                     record.inverseDataRatio +
    //                     record.unknownDataRatio)) *
    //                   100
    //               )}
    //               %
    //             </a>
    //           </span>
    //         </Tooltip>
    //       </div>
    //       <div className="text-xs text-left">
    //         专家数量: <a href="">{record.expert_num}</a>
    //       </div>
    //       <div className="text-xs text-left">
    //         分析周期:{" "}
    //         <a>
    //           {record.analysis_period === 5
    //             ? "5分钟"
    //             : record.analysis_period === 60
    //             ? "一小时"
    //             : "一天"}
    //         </a>
    //       </div>
    //     </a>
    //   ),
    // },
    // {
    //   title: "📅 创建日期",
    //   fixed: "right",
    //   width: 120,
    //   dataIndex: "update_time",
    //   key: "update_time",
    //   render: (date) => {
    //     return date ? moment.utc(date).format("YYYY-MM-DD") : null;
    //   },
    // },
    {
      title: "训练时间",
      dataIndex: "train_time",
      key: "train_time",
      align: "center",
      render: (_, record) => {
        return (
          <TimeRangeDisplay
            startTime={record.train_start_time}
            endTime={record.train_end_time}
            color="blue"
          />
        );
      },
    },
    {
      title: "⚙️ 操作",
      fixed: "right",
      width: 100,
      key: "action",
      render: (_, record) => {
        return (
          <div className="action-menu">
            <Button type="text" size="small">
              开始
            </Button>
            <Button
              type="link"
              size="small"
              onClick={() => {
                const {
                  test_end_time,
                  test_start_time,
                  train_end_time,
                  train_start_time,
                  ...rest
                } = record;
                form.setFieldsValue({
                  training_period: [
                    train_start_time ? moment.utc(train_start_time) : null,
                    train_end_time ? moment.utc(train_end_time) : null,
                  ],
                  testing_period: [
                    test_start_time ? moment.utc(test_start_time) : null,
                    test_end_time ? moment.utc(test_end_time) : null,
                  ],
                  ...rest,
                });
                setOpen(true);
              }}
            >
              编辑
            </Button>
            <Button
              type="link"
              size="small"
              danger
              onClick={() => {
                modal.confirm({
                  title: "确认删除",
                  content: "确定要删除此分析任务吗？",
                  okText: "确认",
                  cancelText: "取消",
                  onOk: () => handleDelete(record.id),
                });
              }}
            >
              删除
            </Button>
          </div>
        );
      },
    },
  ];

  const handleDelete = async (id: number) => {
    try {
      await deleteTaskAnalysisData(id);
      await fetchData();
      message.success("删除成功");
    } catch (error) {
      message.error("删除失败");
    }
  };

  return (
    <App>
      {contextHolderModal}
      <FeatureDetailModal
        taskId={currentTaskId}
        visible={featureModalVisible}
        onCancel={() => setFeatureModalVisible(false)}
      />
      <Table
        columns={columns}
        // scroll={{ x: 'max-content' }}
        dataSource={data}
        rowKey={({ id }) => id}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          onChange: onPaginationChange,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        {...props}
      />
    </App>
  );
};

export default TaskTable;

export const TagRender: React.FC<{
  names: ReactNode[];
  width?: string | number;
}> = ({ names, width }) => {
  const [expanded, setExpanded] = React.useState(false);
  const visibleNames = expanded && names?.length > 3 ? names : names?.slice(0, 3);
  const ref = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    //添加事件如果点击其他地方则折起来
    const handleClick = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        setExpanded(false);
      }
    };
    document.addEventListener("click", handleClick);

    return () => {
      document.removeEventListener("click", handleClick);
    };
  }, [expanded]);

  return (
    <div
      className=" overflow-hidden flex justify-between items-center"
      ref={ref}
      style={{
        width,
      }}
    >
      <Space size={0} wrap className="max-w-80 max-h-80 overflow-auto">
        {visibleNames?.map((name, index) => (
          <Tag key={`market-${index}`} className="tag dark:bg-dark-bg-200">
            {name}
          </Tag>
        ))}
      </Space>
      {names?.length > 3 && (
        <Button type="link" size="small" onClick={() => setExpanded(!expanded)}>
          {expanded ? "收起" : `+${names?.length - 3}`}
        </Button>
      )}
    </div>
  );
};
