
import { NextResponse } from 'next/server';
import { ApiResponseHandler } from '@/lib/api/response';
import {ForecastRepository} from './repositories';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1');
  const pageSize = parseInt(searchParams.get('pageSize') || '10');
  const model_id = parseInt(searchParams.get('model_id') || '0');

  if (!model_id) {
    return ApiResponseHandler.error('model_id参数必填', 400);
  }

  try {
    const models = await ForecastRepository.getForecasts(
      model_id,
      page,
      pageSize,
    );
    return ApiResponseHandler.success(models);
  } catch (error) {
    console.error('获取分析模型选项失败:', error);
    return ApiResponseHandler.error('获取分析模型选项失败');
  }
}