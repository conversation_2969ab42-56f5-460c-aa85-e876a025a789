import React, { useState } from "react";
import { Typography } from "antd";
import { useTheme } from "@/contexts/ThemeContext";
import { tree } from "next/dist/build/templates/app-page";

const { Text, Paragraph } = Typography;

interface NewsItemProps {
  title: string;
  summary: string;
  onClick?: () => void;
  cursor?: string;
}

const NewsItem: React.FC<NewsItemProps> = ({
  title,
  summary,
  onClick,
  cursor = "default",
}) => {
  const { isDark } = useTheme();
  const [expanded, setExpanded] = useState(false);

  return (
    <div className="flex flex-col gap-1">
      <Text
        style={{
          color: isDark ? "#FFFFFFD9" : "rgba(0, 0, 0, 0.8)",
          cursor,
          fontWeight: "500",
        }}
        onClick={onClick}
      >
        {title}
      </Text>
      <Paragraph
        ellipsis={{
          rows: 1,
          expandable: "collapsible",
          expanded,
          onExpand: (_, info) => setExpanded(info.expanded),
        }}
        className="text-xs text-gray-500 dark:text-gray-400 m-0"
      >
        {summary}
      </Paragraph>
    </div>
  );
};

export default NewsItem;
