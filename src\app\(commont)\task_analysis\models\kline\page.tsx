
'use client';


import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Kline<PERSON>hart, { ChartData } from './KlineChart';
import { getModelKline } from '../../services/taskAnalysisService';
import { TARGET_TYPE } from '@/constants';




const Page = () => {
    const searchParams = useSearchParams();
    const model_id = searchParams.get('model_id');
    const [target_type, setTargetType] = useState<number>(0);
    const [loading, setLoading] = useState<boolean>(true);
    const [state, setState] = useState<ChartData>({
        main: [] ,
    });

    useEffect(() => {
        if (!model_id) return;

        const setupKlineData = async () => {
            try {
                setLoading(true);
                const res = await getModelKline(Number(model_id));
                setState({
                   main: res.main,
                });
                setTargetType(res.target_type || 0);
            } catch (error) {
                console.error('Failed to load kline data:', error);
            } finally {
                setLoading(false);
            }
        };

        setupKlineData();
    }, [model_id]);

    return (
        <div className="min-h-screen flex flex-col p-6 bg-gray-50 dark:bg-gray-900">
            <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    K线图分析
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                    模型ID: {model_id} | 预测类型: {TARGET_TYPE[target_type as keyof typeof TARGET_TYPE]}
                </p>
            </div>
            <div className="flex-1">
                {loading ? (
                    <div className="w-full h-[600px] rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 flex items-center justify-center">
                        <div className="flex flex-col items-center space-y-4">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                            <p className="text-gray-600 dark:text-gray-400">加载K线数据中...</p>
                        </div>
                    </div>
                ) : (
                    <KlineChart main={state.main} targetType={target_type} />
                )}
            </div>
        </div>
    );
};

export default Page;