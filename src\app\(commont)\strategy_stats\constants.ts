// 市场类型枚举
export enum MarketType {
  A = "A",      // A股
  HK = "HK",    // 港股
  USA = "USA"   // 美股
}

// 市场类型显示名称映射
export const MARKET_TYPE_LABELS = {
  [MarketType.A]: "A股",
  [MarketType.HK]: "港股", 
  [MarketType.USA]: "美股"
} as const;

// 市场类型选项
export const MARKET_TYPE_OPTIONS = [
  { value: MarketType.A, label: MARKET_TYPE_LABELS[MarketType.A] },
  { value: MarketType.HK, label: MARKET_TYPE_LABELS[MarketType.HK] },
  { value: MarketType.USA, label: MARKET_TYPE_LABELS[MarketType.USA] },
];

// 周期选项
export const PERIOD_OPTIONS = [
  { value: 60, label: "1小时" },
  { value: 1440, label: "1天" },
];

/**
 * 将周期数值转换为可读的时间描述
 * @param period 周期数值
 * @returns 格式化的周期描述
 */
export function formatPeriod(period: number): string {
  if (period === 60) {
    return "1小时";
  } else if (period === 1440) {
    return "1天";
  } else if (period < 60) {
    return `${period}分钟`;
  } else if (period < 1440) {
    const hours = Math.floor(period / 60);
    const minutes = period % 60;
    return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
  } else {
    const days = Math.floor(period / 1440);
    const remainingMinutes = period % 1440;
    if (remainingMinutes === 0) {
      return `${days}天`;
    } else {
      const hours = Math.floor(remainingMinutes / 60);
      const minutes = remainingMinutes % 60;
      let result = `${days}天`;
      if (hours > 0) result += `${hours}小时`;
      if (minutes > 0) result += `${minutes}分钟`;
      return result;
    }
  }
}

/**
 * 获取市场类型的显示名称
 * @param marketType 市场类型代码
 * @returns 市场类型显示名称
 */
export function getMarketTypeLabel(marketType: string): string {
  return MARKET_TYPE_LABELS[marketType as MarketType] || marketType;
}
