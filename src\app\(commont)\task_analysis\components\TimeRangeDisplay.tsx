import React from 'react';
import moment from 'moment';

interface TimeRangeDisplayProps {
  startTime?: string | null;
  endTime?: string | null;
  color?: 'blue' | 'green' | 'red' | 'yellow';
  className?: string;
}

const TimeRangeDisplay: React.FC<TimeRangeDisplayProps> = ({
  startTime,
  endTime,
  color = 'blue',
  className = '',
}) => {
  if (!startTime || !endTime) return null;

  const colorMap = {
    blue: {
      text: 'text-blue-600',
      arrow: 'text-blue-400',
    },
    green: {
      text: 'text-green-600',
      arrow: 'text-green-400',
    },
    red: {
      text: 'text-red-600',
      arrow: 'text-red-400',
    },
    yellow: {
      text: 'text-yellow-600',
      arrow: 'text-yellow-400',
    },
  };

  return (
    <div className={`flex flex-col  ${className}`}>
      <span className={`font-semibold text-xs text-center `}>
        {moment.utc(startTime).format('YYYY-MM-DD HH:mm:ss')}
      </span>
      <span className={`flex items-center justify-center ${colorMap[color].arrow}`}>
        <span className="mx-2">↓</span>
      </span>
      <span className={`text-xs text-center `}>
        {moment.utc(endTime).format('YYYY-MM-DD HH:mm:ss')}
      </span>
    </div>
  );
};

export default TimeRangeDisplay;