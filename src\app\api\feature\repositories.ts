import db from "@/lib/quantDB";
import { FeatureIndices } from "@/types/feature";

// 查询所有指数数据
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page?:number
}

export async function getAllIndices(
  page: number = 1,
  pageSize: number = 10,
  name?: string,
  type?: string[]
): Promise<PaginatedResult<FeatureIndices>> {
  try {
    // 构建查询条件
    let whereClause = "";
    const queryParams: any[] = [];

    if (!!name) {
      whereClause += " WHERE name LIKE ?";
      queryParams.push(`%${name}%`);
    }

    if (type && type.length > 0) {
      whereClause += whereClause ? " AND" : " WHERE";
      whereClause += " type IN (?)";
      queryParams.push(type);
    }

    // 查询总数
    const [countResult] = await db.query(
      `SELECT COUNT(*) as total FROM markets${whereClause}`,
      [...queryParams]
    );
    const total = (countResult as any)[0].total;

    // 查询分页数据
    const offset = (page - 1) * pageSize;
    const sql = `SELECT * FROM markets${whereClause} LIMIT ? OFFSET ?`;
    const [row] = await db.query(sql, [...queryParams, pageSize, offset]);

    if (total && (row as any[]).length === 0 && offset !== 0) {
      const [res] = await db.query(sql, [
        ...queryParams,
        pageSize,
        Math.ceil(total / pageSize),
      ]);

      return {
        data: res as FeatureIndices[],
        total,
      };
    }

    return {
      data: row as FeatureIndices[],
      total,
    };
  } catch (error) {
    console.error("获取指数数据失败:", error);
    throw new Error("获取指数数据失败");
  }
}
