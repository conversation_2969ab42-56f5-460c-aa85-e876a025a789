import { NextRequest } from 'next/server';
import { ApiResponseHandler } from '@/lib/api/response';
import { StrategyTradeDetailsService } from '../services';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const summaryId = searchParams.get('summary_id');
    
    if (!summaryId) {
      return ApiResponseHandler.error('缺少summary_id参数', 400);
    }

    const stats = await StrategyTradeDetailsService.getTradeStatsBySummaryId(parseInt(summaryId));

    return ApiResponseHandler.success(stats, '获取统计信息成功');

  } catch (error) {
    console.error('获取交易统计信息失败:', error);
    const errorMessage = error instanceof Error ? error.message : '获取统计信息失败';
    return ApiResponseHandler.error(errorMessage, 500);
  }
}
