"use client";

import React, { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { Table, Tag, message } from "antd";
import type { ColumnsType } from "antd/es/table";
import moment from "moment";
import { FORECAST_STATUS } from "@/constants";
import { TagRender } from "../../components/TaskTable";
import {
  ForecastType,
  getForecastData,
} from "../../services/taskAnalysisService";

export default function ForecastDetailPage() {
  const searchParams = useSearchParams();
  const modelId = searchParams.get("model_id");

  const [forecastData, setForecastData] = useState<ForecastType[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<{
    current: number;
    pageSize: number;
    total?: number;
  }>({
    current: 1,
    pageSize: 100,
    total: 0,
  });

  const fetchData = async (page?: number, pageSize?: number) => {
    if (!modelId) return;
    try {
      setLoading(true);
      const { data, pagination: backPagination } = await getForecastData(
        Number(modelId),
        page ?? pagination.current,
        pageSize ?? pagination.pageSize
      );
      setForecastData(data);
      setPagination({
        current: backPagination.current,
        pageSize: backPagination.pageSize,
        total: backPagination.total,
      });
    } catch (error) {
      message.error("获取预测数据失败");
    } finally {
      setLoading(false);
    }
  };

  const renderForecastStatus = (status: number) => {
    switch (status) {
      case 0:
        return <span className="text-red-500">{FORECAST_STATUS[status]}</span>;
      case 1:
        return <Tag>{FORECAST_STATUS[status]}</Tag>;
      case 2:
        return (
          <span className="text-green-500">{FORECAST_STATUS[status]}</span>
        );
    }
  };

  const renderForecastResult = (target:number,result: number) => {
    switch (target) {
      case 2:
        switch (result) {
          case 0:
            return <span className="text-red-500">下跌</span>;
          case 1:
            return <span className="text-green-500">上涨</span>;
        }
      case 3:
        switch (result) {
          case 0:
            return <span className="text-red-500">下跌</span>;
          case 1:
            return <span className="dark:text-dark-text-200">震荡</span>;
          case 2:
            return <span className="text-green-500">上张</span>;
        }
      
    }
  }

  useEffect(() => {
    if (modelId) {
      fetchData();
    }
  }, [modelId]);

  const columns: ColumnsType<ForecastType> = [
    {
      title: "特征列表",
      dataIndex: "feature_list",
      key: "feature_list",
      render: (features: string[]) => <TagRender names={features} />,
    },
    {
      title: "最后训练时间",
      dataIndex: "last_train_time",
      key: "last_train_time",
      width: 180,
      render: (text) => {
        if (!text) return "";
        return moment.utc(text).format("YYYY-MM-DD HH:mm:ss");
      },
    },
    {
      title: "预测时间",
      dataIndex: "forecast_time",
      key: "forecast_time",
      width: 180,
      render: (text) => {
        if (!text) return "";
        return moment.utc(text).format("YYYY-MM-DD HH:mm:ss");
      },
    },
    {
      title: "预测状态",
      dataIndex: "forecast_status",
      key: "forecast_status",
      render: (forecast_status: -1 | 0 | 1, record) => {
        const status = forecast_status + 1;
        if (!forecast_status) return "";
        return (
          <div>
            <div className="">
              <span className="font-semibold text-dark-text-100">
                预测状态： {renderForecastStatus(status)} |{" "}
              </span>
              <span className="font-semibold">预测结果：</span>
              <span
                className={
                  Number(record.forecast_outcomes)
                    ? "text-red-500"
                    : "text-green-500"
                }
              >
                {renderForecastResult(record.target_type, Number(record.forecast_outcomes))}
              </span>
            </div>
            <div>
              <span className="font-semibold">预测概率：</span>
              {record.forecast_probability}
            </div>
            <div>
              <span className="font-semibold">实际结果：</span>
              {record.actual_outcomes}
            </div>
          </div>
        );
      },
    },
    {
      title: "报告文件",
      dataIndex: "report_file",
      key: "report_file",
    },
    {
      title: "创建时间",
      dataIndex: "create_time",
      key: "create_time",
      width: 180,
      render: (text) => {
        if (!text) return "-";
        return moment.utc(text).format("YYYY-MM-DD HH:mm:ss");
      },
    },
    {
      title: "更新时间",
      dataIndex: "update_time",
      key: "update_time",
      width: 180,
      render: (text) => {
        if (!text) return "-";
        return moment.utc(text).format("YYYY-MM-DD HH:mm:ss");
      },
    },
  ];

  return (
    <div className="p-4">
      <h1 className="text-xl dark:text-dark-text-100 font-bold mb-2">
        模型预测详情
      </h1>
      <h2 className="text-base dark:text-dark-text-200  mb-4">
        模型ID: {modelId} 模型名称: {forecastData[0]?.model_name}
      </h2>
      <Table
        dataSource={forecastData}
        columns={columns}
        rowKey="id"
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          onChange(page, pageSize) {
            fetchData(page, pageSize);
          },
        }}
        loading={loading}
      />
    </div>
  );
}
