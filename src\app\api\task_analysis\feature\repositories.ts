import pool from "@/lib/quantDB";

export class FeatureRepository {
  static async getFeatures(task_id: number, page: number, pageSize: number) {
    const offset = (page - 1) * pageSize;
    const query = `
      SELECT * FROM ai_analysis_features 
      WHERE task_id = ? 
      LIMIT ? OFFSET ?
    `;
    const countQuery = `
      SELECT COUNT(*) as total FROM ai_analysis_features 
      WHERE task_id = ?
    `;

    try {
      const [row] = await pool.execute(countQuery, [task_id]);
      const total = (row as any)[0].total;
      const [features] = await pool.query(query, [task_id, pageSize, offset]);

      return {
        data: features,
        pagination: {
          total,
          current: page,
          pageSize,
          totalPages: Math.ceil(total / pageSize),
        },
      };
    } catch (error) {
      console.error("获取特征数据失败:", error);
      throw error;
    }
  }

}