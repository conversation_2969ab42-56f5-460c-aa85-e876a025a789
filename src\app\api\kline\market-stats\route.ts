import { NextRequest } from 'next/server';
import { Api<PERSON>esponseHand<PERSON> } from '@/lib/api/response';

export async function GET(request: NextRequest) {
  try {
    // 在实际项目中，这里应该从真实的数据源获取市场统计数据
    // 这里提供模拟数据作为示例
    
    const mockMarketStats = {
      total_stocks: 5234,
      up_count: 2156,
      down_count: 2834,
      flat_count: 244,
      limit_up_count: 45,
      limit_down_count: 12,
      // 可以添加更多统计数据
      turnover_total: 1250000000000, // 总成交额（元）
      volume_total: 45600000000,     // 总成交量（股）
      avg_change_percent: -0.85,     // 平均涨跌幅
      market_cap_total: 85000000000000, // 总市值（元）
    };

    // 添加一些随机波动使数据更真实
    const randomFactor = () => Math.floor(Math.random() * 100) - 50; // -50 到 +50 的随机数
    
    const realTimeStats = {
      total_stocks: mockMarketStats.total_stocks + randomFactor(),
      up_count: Math.max(0, mockMarketStats.up_count + randomFactor()),
      down_count: Math.max(0, mockMarketStats.down_count + randomFactor()),
      flat_count: Math.max(0, mockMarketStats.flat_count + Math.floor(randomFactor() / 5)),
      limit_up_count: Math.max(0, mockMarketStats.limit_up_count + Math.floor(randomFactor() / 10)),
      limit_down_count: Math.max(0, mockMarketStats.limit_down_count + Math.floor(randomFactor() / 10)),
      turnover_total: mockMarketStats.turnover_total + (randomFactor() * 1000000000),
      volume_total: mockMarketStats.volume_total + (randomFactor() * 10000000),
      avg_change_percent: Number((mockMarketStats.avg_change_percent + (randomFactor() / 100)).toFixed(2)),
      market_cap_total: mockMarketStats.market_cap_total + (randomFactor() * 100000000000),
      update_time: new Date().toISOString(),
    };

    // 确保数据一致性
    const total = realTimeStats.up_count + realTimeStats.down_count + realTimeStats.flat_count;
    if (total !== realTimeStats.total_stocks) {
      const diff = realTimeStats.total_stocks - total;
      realTimeStats.flat_count = Math.max(0, realTimeStats.flat_count + diff);
    }

    return ApiResponseHandler.success(realTimeStats);

  } catch (error) {
    console.error('获取市场统计失败:', error);
    return ApiResponseHandler.error('服务器内部错误');
  }
}

// 获取分市场统计
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { markets } = body; // ['SH', 'SZ', 'BJ', 'HK'] 等

    const marketStatsMap = {
      'SH': {
        market: 'SH',
        name: '上海证券交易所',
        total_stocks: 1856,
        up_count: 756,
        down_count: 987,
        flat_count: 113,
        limit_up_count: 15,
        limit_down_count: 3,
        main_index: {
          code: '000001',
          name: '上证指数',
          value: 3245.67,
          change: -15.23,
          change_percent: -0.47,
        }
      },
      'SZ': {
        market: 'SZ',
        name: '深圳证券交易所',
        total_stocks: 2834,
        up_count: 1156,
        down_count: 1534,
        flat_count: 144,
        limit_up_count: 25,
        limit_down_count: 8,
        main_index: {
          code: '399001',
          name: '深证成指',
          value: 10856.34,
          change: -89.45,
          change_percent: -0.82,
        }
      },
      'BJ': {
        market: 'BJ',
        name: '北京证券交易所',
        total_stocks: 234,
        up_count: 98,
        down_count: 124,
        flat_count: 12,
        limit_up_count: 3,
        limit_down_count: 1,
        main_index: {
          code: '899050',
          name: '北证50',
          value: 856.78,
          change: 5.67,
          change_percent: 0.67,
        }
      },
      'HK': {
        market: 'HK',
        name: '香港证券交易所',
        total_stocks: 2567,
        up_count: 1034,
        down_count: 1345,
        flat_count: 188,
        limit_up_count: 0, // 港股无涨跌停限制
        limit_down_count: 0,
        main_index: {
          code: 'HSI',
          name: '恒生指数',
          value: 17234.56,
          change: -123.45,
          change_percent: -0.71,
        }
      }
    };

    let result;
    
    if (markets && Array.isArray(markets)) {
      // 返回指定市场的统计
      result = markets.map(market => marketStatsMap[market]).filter(Boolean);
    } else {
      // 返回所有市场统计
      result = Object.values(marketStatsMap);
    }

    return ApiResponseHandler.success(result);

  } catch (error) {
    console.error('获取分市场统计失败:', error);
    return ApiResponseHandler.error('服务器内部错误');
  }
}
