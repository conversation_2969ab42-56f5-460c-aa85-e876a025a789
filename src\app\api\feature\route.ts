import { getAllIndices } from "./repositories";
import { ApiResponseHandler } from "@/lib/api/response";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = Number(searchParams.get("page")) || 1;
    const pageSize = Number(searchParams.get("pageSize")) || 10;
    const name = searchParams.get("name") || undefined ;
    const type = searchParams.get("type") ;

    console.log(name, type);

    if (page < 1 || pageSize < 1) {
      return ApiResponseHandler.error("页码和每页大小必须大于0", 400);
    }

    const result = await getAllIndices(page, pageSize, name, type?.split(","));
    return ApiResponseHandler.success({
      data: result.data,
      total: result.total,
      page,
      pageSize,
    });
  } catch (error) {
    console.error("获取指数数据失败:", error);
    return ApiResponseHandler.error("获取指数数据失败", 500);
  }
}

export const dynamic = "force-dynamic";
