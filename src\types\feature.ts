interface FeatureIndices  {
    id: number;                // 主键(int)
    internal_id: number;       // 普通int字段
    code: string;              // varchar(20)
    name: string;              // varchar(100)
    region: string;            // varchar(50)
    created_at: Date | string; // timestamp（实际使用中可能是Date或ISO格式字符串）
    updated_at: Date | string; // timestamp
  }

  interface FeatureIndicesDetail {
    id: number;                  // int(1), 主键
    index_id: number;            // int
    date: string;                // date 类型（ISO 格式字符串，如 "2023-01-01"）
    open: number;                // decimal(10,2)
    high: number;                // decimal(10,2)
    low: number;                 // decimal(10,2)
    close: number;               // decimal(10,2)
    volume: number;              // decimal(20,2)
    amount: number;              // decimal(20,2)
    created_at: string | Date;   // timestamp
  }


  export {
    type FeatureIndices,
    type FeatureIndicesDetail

  }