"use client";

import React, { useEffect } from "react";
import {
  Form,
  Row,
  Col,
  Input,
  Button,
  Space,
  DatePicker,
  InputNumber,
  Select,
  ConfigProvider,
} from "antd";
import { useWatch } from "antd/es/form/Form";
import classNames from "classnames";
import { omit } from "lodash";
import { FormItemProps } from "antd/lib";
const { RangePicker } = DatePicker;

export const OPERATORS = [
  { value: "=", label: "=", tooltip: "精确匹配指定的日期或数值" },
  { value: ">=", label: "≥", tooltip: "大于或等于指定的日期或数值" },
  { value: "<=", label: "≤", tooltip: "小于或等于指定的日期或数值" },
  { value: ">", label: ">", tooltip: "大于指定的日期或数值" },
  { value: "<", label: "<", tooltip: "小于指定的日期或数值" },
  {
    value: "between",
    label: "~",
    tooltip: "在两个指定的日期或数值之间（包含边界值）",
  },
];

export type colsType = FormItemProps & {
  value: string;
  span?: number;
  order?: number;
};

const FieldComponent: React.FC<{
  field: string;
  onChange?: (value: any) => void;
  value?: {
    operator: string;
    value1?: string | number;
    value2?: string | number;
  };
  isNumberField?: (field: string) => boolean;
  isTimestampField?: (field: string) => boolean;
}> = ({ field, value, onChange, isNumberField, isTimestampField }) => {
  const handleChange = (key: string, val: any) => {
    onChange?.({
      ...value,
      [key]: val,
    });
  };

  useEffect(() => {
    handleChange("operator", "=");
  }, []);

  return (
    <ConfigProvider
      theme={{
        components: {
          Select: {
            showArrowPaddingInlineEnd: 0,
          },
        },
      }}
    >
      <Space.Compact className="w-full">
        <Select
          className={classNames(
            "!w-[50px] h-[40px] text-sm",
            "[&>input]:text-sm [&>input::placeholder]:text-sm"
          )}
          size="large"
          options={OPERATORS.map((op) => ({
            value: op.value,
            label: op.label,
          }))}
          value={value?.operator}
          suffixIcon={false}
          onChange={(val) => handleChange("operator", val)}
        />
        {isNumberField?.(field) ? (
          <>
            <InputNumber
              className={classNames(
                "h-[40px] text-sm flex-1",
                "[&>input]:text-sm [&>input::placeholder]:text-sm",
                "[&_.ant-input-number-input-wrap>input::placeholder]:text-sm [&_.ant-input-number-input-wrap>input::placeholder]:!leading-6  [&_.ant-input-number-input-wrap>input::placeholder]:text-gray-500"
              )}
              size="large"
              controls={false}
              value={value?.value1}
              onChange={(e) => handleChange("value1", e)}
            />
            {value?.operator === "between" && (
              <InputNumber
                controls={false}
                className={classNames(
                  "h-[40px] text-sm flex-1",
                  "[&>input]:text-sm [&>input::placeholder]:text-sm",
                  "[&_.ant-input-number-input-wrap>input::placeholder]:text-sm [&_.ant-input-number-input-wrap>input::placeholder]:!leading-6  [&_.ant-input-number-input-wrap>input::placeholder]:text-gray-500"
                )}
                size="large"
                value={value?.value2}
                onChange={(e) => handleChange("value2", e)}
              />
            )}
          </>
        ) : (
          <>
            {value?.operator === "between" ? (
              <RangePicker
                className={classNames(
                  "flex-1",
                  "[&_.ant-picker-input>input::placeholder]:!leading-6",
                  "[&_.ant-picker-input>input::placeholder]:!text-sm",
                  "[&_.ant-picker-input>input::placeholder]:!text-gray-500"
                )}
                size="large"
                format={"YYYY-MM-DD"}
                showTime={isTimestampField?.(field)}
                onChange={(val, valString) =>
                  onChange?.({
                    operator: "between",
                    value1: valString[0],
                    value2: valString[1],
                  })
                }
                placeholder={["开始时间", "结束时间"]}
              />
            ) : (
              <DatePicker
                className={classNames(
                  "flex-1",
                  "[&_.ant-picker-input>input::placeholder]:!leading-6",
                  "[&_.ant-picker-input>input::placeholder]:!text-sm",
                  "[&_.ant-picker-input>input::placeholder]:!text-gray-500"
                )}
                size="large"
                showTime={isTimestampField?.(field)}
                onChange={(val, valString) => handleChange("value1", valString)}
              />
            )}
          </>
        )}
      </Space.Compact>
    </ConfigProvider>
  );
};

export const SearchForm: React.FC<{
  form: any;
  onSearch: () => void;
  onReset: () => void;
  numberFields?: string[];
  timestampFields?: string[];
  cols: colsType[];
  elseFilter?: React.ReactNode;
  actionSpan?: number;
}> = ({
  form,
  onSearch,
  onReset,
  numberFields,
  timestampFields,
  cols,
  elseFilter,
  actionSpan
}) => {
  const selectedFields = useWatch("fields", form) || [];
  const isNumberField = (field: string) => {
    return numberFields?.includes(field) ?? false;
  };

  const isTimestampField = (field: string) => {
    return timestampFields?.includes(field) ?? false;
  };

  return (
    <Form form={form}>
      <Row gutter={[16, 8]} className="mb-4 !mx-0">
        <Col span={24} className="dark:bg-dark-bg-200 p-4 rounded-md">
          <Row gutter={[16, 0]}>
            <Col span={6}>
              <Form.Item name="fields" className="!mb-0">
                <Select
                  mode="multiple"
                  maxTagCount="responsive"
                  size="large"
                  prefix={
                    <span className="text-sm leading-10 font-bold mr-2 dark:text-dark-text-100">
                      更多筛选
                    </span>
                  }
                  className="select-base"
                  placeholder="选择搜索条件"
                  allowClear
                  options={cols}
                />
              </Form.Item>
            </Col>
            {elseFilter}
            <Col span={elseFilter?actionSpan:18}>
              <Form.Item className="flex items-center justify-end !mb-0">
                <Space>
                  <Button type="primary" onClick={onSearch}>
                    搜索
                  </Button>
                  <Button type="primary" onClick={onReset}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Col>

        {selectedFields.length > 0 && (
          <Col span={24} className="dark:bg-dark-bg-200 pt-4 px-4 rounded-md">
            <Row gutter={[16, 0]}>
              {selectedFields.map((field: string, index: number) => {
                const currentField = cols.find((col) => col.value === field);
                return (
                  <Col
                    key={field}
                    span={
                      (currentField?.span ?? 4) +
                      (form.getFieldValue(field)?.operator === "~" ? 2 : 0)
                    }
                    order={currentField?.order ?? index + 1}
                  >
                    <Form.Item
                      name={field}
                      colon={false}
                      label={
                        <p className="text-sm font-bold mr-2 dark:text-dark-text-200 !leading-[40px]">
                          {currentField?.label}
                        </p>
                      }
                      className="mb-0 [&_.ant-form-item-label>label]:text-sm [&_.ant-form-item-label>label]:leading-[40px] [&_.ant-form-item-label>label]:!h-[40px]"
                      {...omit(currentField, [
                        "value",
                        "label",
                        "span",
                        "order",
                      ])}
                    >
                      <FieldComponent
                        field={field}
                        isNumberField={isNumberField}
                        isTimestampField={isTimestampField}
                      />
                    </Form.Item>
                  </Col>
                );
              })}
            </Row>
          </Col>
        )}
      </Row>
    </Form>
  );
};
