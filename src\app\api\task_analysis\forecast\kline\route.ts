import { NextResponse } from "next/server";
import { ApiResponseHandler } from "@/lib/api/response";
import { ForecastRepository } from "../repositories";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const modelId = searchParams.get("model_id");

  if (!modelId) {
    return NextResponse.json(
      { error: "model_id is required" },
      { status: 400 }
    );
  }

  try {
    const target_type = await ForecastRepository.getModelTargetById(Number(modelId));
    const response = await fetch(
      `http://115.159.23.103:5001/api/predict?model_id=${modelId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      }
    );
    if (!response.ok) {
      const errorText = await response.text();
      return ApiResponseHandler.error(
        `HTTP error! status: ${response.status} ${errorText} `
      );
    }

    // 获取文本内容并尝试处理
    const text = await response.text();
    try {
      const json = JSON.parse(text);
      return ApiResponseHandler.success({
        ...json,
        target_type: target_type?.target_type || 0, // 默认值为0
      });
    } catch (parseError) {
      console.error("JSON Parse Error:", parseError);
      console.error("Raw response:", text.substring(0, 200) + "...");
      return ApiResponseHandler.error(
        "Invalid JSON response from server" + parseError + text
      );
    }
  } catch (error) {
    console.log(error);
    return NextResponse.json(
      ApiResponseHandler.error((error as Error).message),
      { status: 500 }
    );
  }
}
