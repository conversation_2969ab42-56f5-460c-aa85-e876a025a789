import { NextResponse } from 'next/server'

interface ApiResponse<T> {
  code: number
  data?: T
  message: string
  success: boolean
}

export class ApiResponseHandler {
  static success<T>(data?: T, message: string = 'success'): NextResponse<ApiResponse<T>> {
    return NextResponse.json({
      code: 200,
      data,
      message,
      success: true
    })
  }

  static error(message: string, code: number = 500): NextResponse<ApiResponse<null>> {
    return NextResponse.json({
      code,
      message,
      success: false
    })
  }
}