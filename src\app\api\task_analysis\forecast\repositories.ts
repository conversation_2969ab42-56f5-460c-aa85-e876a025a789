import pool from "@/lib/quantDB";

/**
 * 预测数据feature_names是把feature_list中的每个元素和ai_analysis_features表task_id相同的中的feature_name
 * 进行匹配，如果匹配成功，则返回feature_name，否则返回feature_item
 * SELECT 
    f.*,
    (
        SELECT JSON_ARRAYAGG(
            CASE 
                WHEN a.feature_name IS NOT NULL THEN a.feature_name
                ELSE feature_item
            END
        )
        FROM JSON_TABLE(
            f.feature_list,
            '$[*]' COLUMNS(
                feature_item VARCHAR(255) COLLATE utf8mb4_unicode_ci PATH '$'
            )
        ) AS features
        LEFT JOIN ai_analysis_features a ON 
            a.feature_code COLLATE utf8mb4_unicode_ci = features.feature_item AND 
            a.task_id = f.task_id
    ) AS feature_names
FROM ai_analysis_forecasts f
WHERE model_id = ? 
LIMIT ? OFFSET ?
 * 
 */
export class ForecastRepository {
  static async getForecasts(model_id: number, page: number, pageSize: number) {
    const offset = (page - 1) * pageSize;
    const query = `
    SELECT f.*
    FROM ai_analysis_forecasts f
    WHERE model_id = ? 
    ORDER BY f.update_time DESC
    LIMIT ? OFFSET ?
    `;
    const countQuery = `
      SELECT COUNT(*) as total FROM ai_analysis_forecasts 
      WHERE model_id = ?
    `;

    try {
      const [row] = await pool.execute(countQuery, [model_id]);
      const total = (row as any)[0].total;
      const [forecasts] = await pool.query(query, [model_id, pageSize, offset]);

      return {
        data: forecasts,
        pagination: {
          total,
          current: page,
          pageSize,
          totalPages: Math.ceil(total / pageSize),
        },
      };
    } catch (error) {
      console.error("获取预测数据失败:", error);
      throw error;
    }
  }

  static async getModelTargetById(id: number) {
    const query = `
    SELECT f.target_type
    FROM ai_analysis_forecasts f
    WHERE f.id = ?
    `;
    try {
      const [rows] = await pool.query(query, [id]);
      return (rows as any)[0] || null;
    } catch (error) {
      console.error("获取预测数据失败:", error);
      throw error;
    }
  }
}
