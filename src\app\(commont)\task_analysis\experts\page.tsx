"use client";

import React from "react";
import { Input } from "antd";
import ExpertTable from "@/app/(commont)/task_analysis/components/ExpertTable";
import { Expert } from "@/app/(commont)/task_analysis/components/ExpertTable";
import { getExperts } from "../services/taskAnalysisService";
import { usePathname, useSearchParams } from "next/navigation";

const { Search } = Input;

const ExpertsPage = () => {
  const [data, setData] = React.useState<Expert[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [pagination, setPagination] = React.useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const pathName = useSearchParams();
  const task_id = pathName.get("id") ?? "";

  // 模拟获取数据
  const fetchData = async (
    current: number,
    pageSize: number,
    task_id: string
  ) => {
    setLoading(true);
    try {
      const data = await getExperts({
        page: current,
        pageSize,
        task_id,
      });
console.log(data);
      setData(data.data);
      setPagination({
        current,
        pageSize,
        total: pagination.total,
      });
    } catch (error) {
      console.error("获取专家数据失败:", error);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    fetchData(pagination.current, pagination.pageSize,task_id );
  }, [pagination.current, pagination.pageSize, task_id]);

  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination({ ...pagination, current: page, pageSize });
  };

  const handleSearch = (value: string) => {
    setSearchValue(value);
    setPagination({ ...pagination, current: 1 });
  };

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Search
          placeholder="输入专家姓名或专业领域"
          allowClear
          enterButton="搜索"
          size="large"
          onSearch={handleSearch}
        />
      </div>
      <ExpertTable
        data={data}
        loading={loading}
        pagination={pagination}
        onPaginationChange={handlePaginationChange}
      />
    </div>
  );
};

export default ExpertsPage;
