import { NextRequest } from "next/server";
import { ApiResponseHandler } from "@/lib/api/response";
import { StrategyTradeDetailsService } from "./services";
import {
  TradeDetailsQueryParams,
  StrategyTradeDetail,
} from "@/types/strategy-trade-details";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // 解析查询参数
    const params: TradeDetailsQueryParams = {
      summary_id: searchParams.get("summary_id")
        ? parseInt(searchParams.get("summary_id")!)
        : undefined,
      test_batch_id: searchParams.get("test_batch_id") || undefined,
      stock_code: searchParams.get("stock_code") || undefined,
      strategy_name: searchParams.get("strategy_name") || undefined,
      trade_status:
        (searchParams.get("trade_status") as "open" | "closed") || undefined,
      page: parseInt(searchParams.get("page") || "1"),
      pageSize: parseInt(searchParams.get("pageSize") || "20"),
      sortField: searchParams.get("sortField") || "entry_time",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "desc",
    };

    // 使用服务层获取数据
    const response = await StrategyTradeDetailsService.getTradeDetailsList(
      params
    );

    return ApiResponseHandler.success(response, "查询成功");
  } catch (error) {
    console.error("查询策略交易详情失败:", error);
    const errorMessage = error instanceof Error ? error.message : "查询失败";
    return ApiResponseHandler.error(errorMessage, 500);
  }
}
