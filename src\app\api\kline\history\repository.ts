import pool from "@/lib/quantDB";

export async function getHistoryKLineData(
  type: string,
  ticker: string,
  period: string,
  start_date: string,
  end_date: string
) {
    let whereClause = ``;
    const queryParams: any[] = [];
    if (type === 'stock') {
      type = 'stock_kline';
    }

    if (ticker) {
      whereClause += ` WHERE ticker = ?`;
      queryParams.push(ticker);
    }
  
    if (start_date && end_date) {
      whereClause += whereClause ? ` AND time >= ? AND time <= ?` : ` WHERE time >= ? AND time <= ?`;
      queryParams.push(start_date, end_date);
    }

    const sql = `SELECT * FROM ${type}_${period} ${whereClause} ORDER BY date ASC`;
   
    const [rows] = await pool.query(sql, queryParams);
    return rows as any[];
}

