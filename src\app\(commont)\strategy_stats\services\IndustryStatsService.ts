import request from '@/utils/request';

export interface IndustryStats {
  industry: string;
  stock_num: number;
  sum_total_trades: number;
  sum_profitable_trades: number;
  sum_losing_trades: number;
  sum_total_profit_pct: number;
  avg_win_rate: number;
}

export const industryStatsService = {
  async getIndustryStats(backtestId: string): Promise<IndustryStats[]> {
    return request.get('/industry-stats', {
      params: { backtestId }
    });
  }
};