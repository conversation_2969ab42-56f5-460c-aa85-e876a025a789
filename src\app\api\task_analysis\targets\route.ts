import { ApiResponseHandler } from '@/lib/api/response';
import { TaskAnalysisRepository } from '../repositories';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const keywords = searchParams.get('keywords') || '';
    const targets = await TaskAnalysisRepository.getTargetOptions(keywords);
    return ApiResponseHandler.success(targets);
  } catch (error) {
    console.error('获取分析目标选项失败:', error);
    return ApiResponseHandler.error('获取分析目标选项失败');
  }
}