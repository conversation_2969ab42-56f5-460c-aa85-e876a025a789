# Strategy Trade Details API

此模块实现了策略交易详情的完整CRUD操作，采用三层架构设计，将数据库操作和业务逻辑分离。

## 架构设计

### 三层架构
- **API路由层 (Route Layer)**: 处理HTTP请求和响应
- **服务层 (Service Layer)**: 处理业务逻辑和数据验证
- **数据仓库层 (Repository Layer)**: 处理数据库操作

### 文件结构
```
strategy-trade-details/
├── route.ts                    # 主API路由 (GET列表, POST创建)
├── [id]/route.ts               # 详情API路由 (GET详情, PUT更新, DELETE删除)
├── stats/route.ts              # 统计API路由 (GET统计信息)
├── repositories.ts             # 数据仓库层
├── services.ts                 # 服务层
└── README.md                   # 说明文档
```

## API 接口

### 1. 获取交易详情列表
- **URL**: `GET /api/strategy-trade-details`
- **参数**:
  - `summary_id`: 汇总ID (可选)
  - `test_batch_id`: 测试批次ID (可选)
  - `stock_code`: 股票代码 (可选，支持模糊搜索)
  - `strategy_name`: 策略名称 (可选)
  - `trade_status`: 交易状态 (可选, 'open'|'closed')
  - `page`: 页码 (默认1)
  - `pageSize`: 每页大小 (默认20)
  - `sortField`: 排序字段 (默认'entry_time')
  - `sortOrder`: 排序方向 (默认'desc')

### 2. 创建交易详情
- **URL**: `POST /api/strategy-trade-details`
- **Body**: StrategyTradeDetail对象 (不包含id, created_at, updated_at)

### 3. 获取单个交易详情
- **URL**: `GET /api/strategy-trade-details/[id]`

### 4. 更新交易详情
- **URL**: `PUT /api/strategy-trade-details/[id]`
- **Body**: 要更新的字段

### 5. 删除交易详情
- **URL**: `DELETE /api/strategy-trade-details/[id]`

### 6. 获取交易统计
- **URL**: `GET /api/strategy-trade-details/stats?summary_id={id}`
- **返回**: 交易统计信息 (总交易数、开仓数、平仓数、总盈亏、平均收益率)

## 特性

### 数据仓库层 (StrategyTradeDetailsRepository)
- `getCount()`: 获取记录总数
- `getList()`: 获取分页列表
- `getById()`: 根据ID获取单条记录
- `create()`: 创建新记录
- `update()`: 更新记录
- `delete()`: 删除记录

### 服务层 (StrategyTradeDetailsService)
- 数据验证和业务逻辑处理
- 错误处理和异常管理
- 统计功能
- 并行查询优化

### 优势
1. **关注点分离**: 数据库操作、业务逻辑、API处理各司其职
2. **代码复用**: 服务层可被多个API路由使用
3. **易于测试**: 各层可独立进行单元测试
4. **易于维护**: 修改数据库结构只需更改Repository层
5. **类型安全**: 全程使用TypeScript严格类型检查
6. **性能优化**: 并行查询减少数据库连接时间

## 错误处理

所有层级都实现了完善的错误处理：
- Repository层: 数据库连接和SQL执行错误
- Service层: 业务逻辑验证和数据转换错误
- Route层: HTTP请求解析和响应格式化错误

## 使用示例

```typescript
// 前端调用示例
import request from '@/utils/request';

// 获取交易详情列表
const response = await request.get('/api/strategy-trade-details', {
  params: {
    summary_id: 1,
    page: 1,
    pageSize: 20
  }
});

// 创建交易详情
const newRecord = await request.post('/api/strategy-trade-details', {
  stock_code: '000001',
  strategy_name: 'BB_Strategy',
  trade_status: 'open',
  // ... 其他字段
});

// 更新交易详情
await request.put(`/api/strategy-trade-details/${id}`, {
  trade_status: 'closed',
  exit_price: 10.50
});

// 获取统计信息
const stats = await request.get('/api/strategy-trade-details/stats', {
  params: { summary_id: 1 }
});
```
