import db from "@/lib/quantDB";

export class TaskAnalysisRepository {
  static async getTrainingModels(
    taskId: number,
    page: number = 1,
    pageSize: number = 10
  ) {
    try {
      const offset = (page - 1) * pageSize;
      // 获取模型基本信息
      const [rows] = await db.query(`
        SELECT 
            m.*,
            COUNT(f.id) AS forecast_count,
            SUM(CASE WHEN f.forecast_status = 1 THEN 1 ELSE 0 END) AS success_count,
            SUM(CASE WHEN f.forecast_status = -1 THEN 1 ELSE 0 END) AS failure_count,
            CASE 
                WHEN COUNT(f.id) = 0 THEN 0 
                ELSE ROUND(
                    (SUM(CASE WHEN f.forecast_status = 1 THEN 1 ELSE 0 END) * 100.0) / 
                    COUNT(f.id), 
                    2
                ) 
            END AS success_rate,
            -- 新增最新预测记录字段 --
            latest.last_train_time AS last_train_time,
            latest.forecast_time AS forecast_time,
            latest.forecast_outcomes AS forecast_outcomes,
            latest.forecast_probability AS forecast_probability,
            -- 按月统计的预测数据（JSON格式） --
            (
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'month_period', monthly.month_period,
                        'total_count', monthly.total_count,
                        'success_count', monthly.success_count,
                        'success_rate_percent', monthly.success_rate_percent
                    )
                )
                FROM (
                    SELECT 
                        DATE_FORMAT(forecast_time, '%Y-%m') AS month_period,
                        COUNT(*) AS total_count,
                        SUM(forecast_status = 1) AS success_count,
                        ROUND(SUM(forecast_status = 1) / COUNT(*) * 100, 2) AS success_rate_percent
                    FROM ai_analysis_forecasts
                    WHERE 
                        model_id = m.id
                        AND forecast_status != 0 
                        AND forecast_time IS NOT NULL
                    GROUP BY DATE_FORMAT(forecast_time, '%Y-%m')
                    ORDER BY DATE_FORMAT(forecast_time, '%Y-%m')
                ) monthly
            ) AS monthly_stats
        FROM 
            ai_analysis_models m
        LEFT JOIN 
            ai_analysis_forecasts f ON m.id = f.model_id
        -- 获取每个模型的最新预测记录 --
        LEFT JOIN (
            SELECT 
                model_id,
                last_train_time,
                forecast_time,
                forecast_outcomes,
                forecast_probability
            FROM (
                SELECT 
                    model_id,
                    last_train_time,
                    forecast_time,
                    forecast_outcomes,
                    forecast_probability,
                    ROW_NUMBER() OVER (PARTITION BY model_id ORDER BY forecast_time DESC) AS rn
                FROM 
                    ai_analysis_forecasts
            ) ranked
            WHERE rn = 1
        ) latest ON m.id = latest.model_id
        WHERE m.task_id = ?
        GROUP BY 
            m.id,
            latest.last_train_time,
            latest.forecast_time,
            latest.forecast_outcomes,
            latest.forecast_probability
        LIMIT ? OFFSET ?`,
    [taskId, pageSize, offset]
      );

      // 获取总数
      const [countRows] = await db.query(
        `SELECT COUNT(*) as total FROM ai_analysis_models WHERE task_id = ?`,
        [taskId]
      );
      const total = (countRows as any)[0].total;

      return {
        data: rows,
        pagination: {
          total,
          current: page,
          pageSize,
          totalPages: Math.ceil(total / pageSize),
        },
      };
    } catch (error) {
      console.error("获取训练模型数据失败:", error);
      throw error;
    }
  }
}
