import { StrategyStats } from "@/app/(commont)/strategy_stats/types";
import pool from "@/lib/quantDB";
import moment from "moment";

// 策略统计数量类型定义
export interface StrategyCountStats {
  strategy_name: string;
  num: number;
}

export interface StrategyStatsRepository {
  getStrategyCount(): Promise<StrategyCountStats[]>;
  getStrategyStats(
    page: number,
    pageSize: number,
    order?: "ASC" | "DESC",
    orderBy?: string,
    strategyName?: string,
    testBatchId?: string,
    marketType?: string,
    period?: number,
    star?: 1 | 0,
    moreSearch?: Array<[string, any]>
  ): Promise<{
    data: StrategyStats[];
    total: number;
  }>;
}



// 允许的操作符列表
const ALLOWED_OPERATORS = new Set(['=', '>=', '<=', '>', '<', 'between']);

export class StrategyStatsRepository implements StrategyStatsRepository {
  async getStrategyCount(): Promise<StrategyCountStats[]> {
    try {
      const [row] = await pool.query(`
        SELECT strategy_name, COUNT(*) as num 
        FROM strategy_backtest_summary 
        GROUP BY strategy_name
      `);
      return row as StrategyCountStats[];
    } catch (error) {
      console.error("获取策略统计数据失败:", error);
      throw error;
    }
  }

  async getStrategyStats(
    page: number = 1,
    pageSize: number = 10,
    order?: "ASC" | "DESC",
    orderBy?: string,
    strategyName?: string,
    testBatchId?: string,
    marketType?: string,
    period?: number,
    star?: 1 | 0,
    moreSearch?: Array<[string, any]>
  ): Promise<{
    data: StrategyStats[];
    total: number;
  }> {
    try {
      // 构建查询条件
      let conditions = [];
      let queryParams = [];

      const [allowMoreSearchKeys] = await pool.query(`SELECT s.key FROM strategy_configs s`);

      if (strategyName) {
        conditions.push("strategy_name = ?");
        queryParams.push(strategyName);
      }

      if (testBatchId) {
        conditions.push("test_batch_id LIKE ?");
        queryParams.push(`%${testBatchId}%`);
      }

      if (marketType) {
        conditions.push("market_type = ?");
        queryParams.push(marketType);
      }

      if (period) {
        conditions.push("period = ?");
        queryParams.push(period);
      }

      if (star !== undefined) {
        conditions.push("star = ?");
        queryParams.push(star);
      }

      if (moreSearch) {
        moreSearch.forEach(([key, value]) => {
          // 检查操作符是否在允许的列表中
          if (value && !ALLOWED_OPERATORS.has(value.operator)) {
            throw new Error(`不支持的操作符: ${value.operator}`);
          }
          // 根据操作符构建查询条件
          if(key && !(allowMoreSearchKeys as {key: string}[]).map(item => item.key).includes(key)) {
            throw new Error(`不支持的搜索条件: ${key}`);
          }

          if (value !== undefined && value.operator !== 'between') {
            conditions.push(`JSON_EXTRACT(strategy_params, '$.${key}') ${value.operator} ?`);
            if (typeof value.value1 === 'string') {
              queryParams.push(value.value1);
            } else {
              queryParams.push(value.value1);
            }
          } else if(value !== undefined && value.operator === 'between') {
            conditions.push(`JSON_EXTRACT(strategy_params, '$.${key}') BETWEEN ? AND ?`);
            if (typeof value.value1 === 'string' && typeof value.value2 === 'string') {
              queryParams.push(value.value1, value.value2);
            } else {
              queryParams.push(value.value1, value.value2);
            }
          }
        }); 
      }

      const whereClause =
        conditions.length > 0 ? `WHERE ${conditions.join(" AND ")}` : "";

      // 获取总数
      const [countResult] = await pool.query(
        `SELECT COUNT(*) as total FROM strategy_backtest_summary ${whereClause}`,
        queryParams
      );
      const total = (countResult as any)[0].total;

      const offset = (page - 1) * pageSize;
      const orderClause =
        orderBy && order
          ? `ORDER BY ${orderBy} ${order}`
          : "ORDER BY created_at DESC";

      // 执行分页查询
      const [rows] = await pool.query(
        `SELECT *,
        total_net_pnl/(avg_holding_days * total_trades) as avg_holding_return
         FROM strategy_backtest_summary
         ${whereClause}
         ${orderClause}
         LIMIT ? OFFSET ?`,
        [...queryParams, pageSize, offset]
      );
      return {
        data: rows as StrategyStats[],
        total,
      };
    } catch (error) {
      console.error("获取策略统计数据失败:", error);
      throw error;
    }
  }

  async updateStar(id: number, star: boolean): Promise<any> {
    try {
      const [result] = await pool.execute(
        'UPDATE strategy_backtest_summary SET star = ? WHERE id = ?',
        [star ? 1 : 0, id]
      );
      return result;
    } catch (error) {
      console.error("更新星标失败:", error);
      throw error;
    }
  }
}

export const strategyStatsRepository = new StrategyStatsRepository();
