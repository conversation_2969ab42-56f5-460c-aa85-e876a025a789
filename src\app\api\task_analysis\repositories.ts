import {
  TaskDataType,
  UpdateTaskDataType,
} from "@/app/(commont)/task_analysis/services/taskAnalysisService";
import { STOCK_TYPE } from "@/constants";
import pool from "@/lib/quantDB";

export class TaskAnalysisRepository {
  static async deleteTask(id: number) {
    const query = `DELETE FROM ai_analysis_task WHERE id = ?`;
    await pool.query(query, [id]);
  }

  static async getTasks(page: number, pageSize: number) {
    const offset = (page - 1) * pageSize;
    const query = `
SELECT a.id,
        (
          SELECT NAME
          FROM markets
          WHERE FIND_IN_SET(id, a.analysis_target)        
        )  AS  target_name,
        
        (
          SELECT JSON_ARRAYAGG(title) 
          FROM openai_configs 
          WHERE JSON_OVERLAPS(a.models, JSON_ARRAY(id)) COLLATE utf8mb4_unicode_ci
        ) AS model_titles,
        (
          SELECT JSON_ARRAYAGG(NAME)
          FROM markets
          WHERE FIND_IN_SET(id, a.market_ids)
        ) AS market_names,
        (SELECT JSON_OBJECTAGG(
          m.type,
          (SELECT JSON_ARRAYAGG(id) 
            FROM markets 
            WHERE FIND_IN_SET(id, a.market_ids) 
            AND TYPE = m.type
            GROUP BY TYPE)
        )
        FROM (SELECT DISTINCT TYPE FROM markets WHERE FIND_IN_SET(id, a.market_ids)) m
        ) AS CODE,
       a.*, 
       (SELECT COUNT(*) FROM ai_analysis_features WHERE task_id = a.id) AS feature_num,
       (SELECT COUNT(*) FROM ai_analysis_models WHERE task_id = a.id) AS model_num
      FROM ai_analysis_task a
      ORDER BY a.create_time DESC
      LIMIT ? OFFSET ?
    `;
    const countQuery = "SELECT COUNT(*) as total FROM ai_analysis_task";

    try {
      const [row] = await pool.execute(countQuery);
      const total = (row as any)[0].total;
      const [tasksFormDB] = await pool.query(query, [pageSize, offset]);

      const tasks = (tasksFormDB as any).map((task: any) => {
        const analysis_target =
          task.analysis_type === 2
            ? task.analysis_target + "indices"
            : task.analysis_target;

        return {
          ...task,
          analysis_target,
        };
      });

      return {
        data: tasks,
        pagination: {
          total,
          current: page,
          pageSize,
          totalPages: Math.ceil(total / pageSize),
        },
      };
    } catch (error) {
      console.error("获取分析任务列表失败:", error);
      throw error;
    }
  }

  static async createTask(taskData: TaskDataType) {
    const query = `
      INSERT INTO ai_analysis_task (
        analysis_target,
        models,
        market_ids,
        analysis_period,
        analysis_purpost,
        technical_indicators,
        expert_num,
        train_start_time,
        train_end_time,
        test_start_time,
        test_end_time,
        create_time,
        update_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(),NOW())
    `;

    try {
      const [result] = await pool.query(query, [
        taskData.analysis_target,
        JSON.stringify(taskData.models),
        taskData.market_ids,
        taskData.analysis_period,
        taskData.analysis_purpost,
        JSON.stringify(taskData.technical_indicators),
        taskData.expert_num,
        taskData.train_start_time,
        taskData.train_end_time,
        taskData.test_start_time,
        taskData.test_end_time,
      ]);

      return result;
    } catch (error) {
      console.error("创建分析任务失败:", error);
      throw error;
    }
  }

  static async updateTask(taskData: UpdateTaskDataType) {
    const query = `
      UPDATE ai_analysis_task
      SET 
        analysis_target = ?,
        models = ?,
        market_ids = ?,
        analysis_purpost = ?,
        technical_indicators = ?,
        expert_num = ?,
        train_start_time = ?,
        train_end_time = ?,
        test_start_time = ?,
        test_end_time = ?,
        update_time = NOW()
      WHERE id = ?
    `;

    try {
      const [result] = await pool.query(query, [
        taskData.analysis_target,
        JSON.stringify(taskData.models),
        taskData.market_ids,
        taskData.analysis_purpost,
        JSON.stringify(taskData.technical_indicators),
        taskData.expert_num,
        taskData.train_start_time,
        taskData.train_end_time,
        taskData.test_start_time,
        taskData.test_end_time,
        taskData.id,
      ]);

      return result;
    } catch (error) {
      console.error("更新分析任务失败:", error);
      throw error;
    }
  }

  //获取模型选项
  static async getModelOptions() {
    const query = `
      SELECT id,model,title FROM openai_configs
    `;

    try {
      const [models] = await pool.execute(query);

      return models;
    } catch (error) {
      console.error("获取分析模型选项失败:", error);
      throw error;
    }
  }
  //获取分析目标
  static async getTargetOptions(keyword?: string) {
    const query = `
      SELECT code,name,type,id 
      FROM markets
      ${keyword ? "WHERE name LIKE ? OR code LIKE ? OR type LIKE ?" : ""}
    `;

    try {
      const stockResult = await pool.execute(query, [
        keyword,
        keyword,
        keyword,
      ]);
      const stock_row = stockResult[0] as Array<{
        code: string;
        name: string;
        type: keyof typeof STOCK_TYPE;
        id: number | string;
      }>;

      return stock_row;
    } catch (error) {
      console.error("获取股票选项失败:", error);
      throw error;
    }
  }
}
