@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary-100: #0872ff;
  --primary-200: #6ec0ff;
  --primary-300: #e3ffff;
  --accent-100: #17202f;
  --accent-200: #212b3a;
  --text-100: #fafafa;
  --text-200: #b9bfcc;
  --bg-100: #1f2937;
  --bg-200: #111827;
  --bg-300: #030712;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 禁用表格背景颜色动画 */
.ant-table-wrapper .ant-table-thead > tr > th,
.ant-table-wrapper .ant-table-tbody > tr > td {
  transition: none !important;
}

.ant-table-cell.ant-table-column-sort {
  @apply bg-dark-bg-100;
}

html.dark {
  .ant-table-header {
    @apply border border-b-0 border-dark-accent-200;

    .ant-table-cell {
      @apply !border-b-0;
    }
  }
}

.ant-pagination .ant-pagination-options-quick-jumper input {
  @apply !bg-dark-bg-100;
}

.ant-picker-time-panel-cell-selected {
  .ant-picker-time-panel-cell-inner {
    @apply !bg-dark-primary-100;
  }
}

.ant-input {
  @apply [&::placeholder]:text-sm [&::placeholder]:!leading-[40px]
}

/* 自定义滚动条样式 */
/* 适用于 Webkit 浏览器 (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 暗色主题下的滚动条 */
html.dark ::-webkit-scrollbar-thumb {
  background: #4b5563;
}

html.dark ::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Firefox 滚动条样式 */
* {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
}

html.dark * {
  scrollbar-color: #4b5563 transparent;
}