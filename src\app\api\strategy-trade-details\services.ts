import { StrategyTradeDetailsRepository } from "./repositories";
import {
  TradeDetailsQueryParams,
  TradeDetailsResponse,
  StrategyTradeDetail,
} from "@/types/strategy-trade-details";

export class StrategyTradeDetailsService {
  /**
   * 获取策略交易详情分页列表
   */
  static async getTradeDetailsList(
    params: TradeDetailsQueryParams
  ): Promise<TradeDetailsResponse> {
    try {
      // 并行获取总数和数据列表
      const [total, data] = await Promise.all([
        StrategyTradeDetailsRepository.getCount(params),
        StrategyTradeDetailsRepository.getList(params),
      ]);

      // 计算分页信息
      const page = params.page || 1;
      const pageSize = params.pageSize || 20;
      const totalPages = Math.ceil(total / pageSize);

      return {
        data,
        total,
        page,
        pageSize,
        totalPages,
      };
    } catch (error) {
      console.error("获取策略交易详情列表失败:", error);
      throw new Error("获取策略交易详情列表失败");
    }
  }

  /**
   * 根据ID获取策略交易详情
   */
  static async getTradeDetailById(
    id: number
  ): Promise<StrategyTradeDetail | null> {
    try {
      return await StrategyTradeDetailsRepository.getById(id);
    } catch (error) {
      console.error(`获取策略交易详情失败 (ID: ${id}):`, error);
      throw new Error("获取策略交易详情失败");
    }
  }

  /**
   * 根据汇总ID获取相关交易详情统计
   */
  static async getTradeStatsBySummaryId(summaryId: number): Promise<{
    totalTrades: number;
    openTrades: number;
    closedTrades: number;
    totalPnl: number;
    avgReturn: number;
  }> {
    try {
      const params: TradeDetailsQueryParams = {
        summary_id: summaryId,
        page: 1,
        pageSize: 999999, // 获取所有数据用于统计
      };

      const result = await this.getTradeDetailsList(params);
      const trades = result.data;

      const totalTrades = trades.length;
      const openTrades = trades.filter((t) => t.trade_status === "open").length;
      const closedTrades = trades.filter(
        (t) => t.trade_status === "closed"
      ).length;
      const totalPnl = trades.reduce(
        (sum, t) => Number(sum) + (Number(t.net_pnl) || 0),
        0
      );
      const validReturns = trades
        .filter((t) => t.return_pct !== null)
        .map((t) => t.return_pct!);
      const avgReturn =
        validReturns.length > 0
          ? validReturns.reduce((sum, r) => sum + r, 0) / validReturns.length
          : 0;

      return {
        totalTrades,
        openTrades,
        closedTrades,
        totalPnl,
        avgReturn,
      };
    } catch (error) {
      console.error(`获取交易统计失败 (Summary ID: ${summaryId}):`, error);
      throw new Error("获取交易统计失败");
    }
  }

  /**
   * 获取所有交易数据用于图表展示
   * @param summaryId 汇总ID
   * @returns 返回交易数据数组
   */
  static async getTradeDataForChart(
    summaryId: number
  ): Promise<StrategyTradeDetail[]> {
    try {
      const result = await StrategyTradeDetailsRepository.getAll(summaryId);
      return result;
    } catch (error) {
      console.error(`获取交易数据失败 (Summary ID: ${summaryId}):`, error);
      throw new Error("获取交易数据失败");
    }
  }
}
