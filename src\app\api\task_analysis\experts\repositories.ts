import pool from "@/lib/quantDB";

export interface Expert {
  id: string;
  name: string;
  specialty: string;
  experience: number;
  rating: number;
  model_name: string;
}

export async function getExpertsByTask(
  taskId: number,
  page: number,
  pageSize: number,
): Promise<{ data: Expert[]; total: number }> {
  let query =`
    SELECT e.*, o.title AS model_name 
    FROM ai_experts AS e 
    LEFT JOIN openai_configs AS o ON o.id = e.model_id 
    WHERE e.task_id = ?
  `;


  const countQuery = `SELECT COUNT(*) FROM (${query}) AS count_query`;
  
  query = `${query} LIMIT ? OFFSET ?`;

  const offset = (page - 1) * pageSize;
  const [experts, countResult] = await Promise.all([
    pool.query(query, [taskId, pageSize, offset]),
    pool.query(countQuery, [taskId])
  ]);

  return {
    data: experts[0],
    total: parseInt(countResult[0][0].count)
  };
}