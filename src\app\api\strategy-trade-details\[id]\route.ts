import { NextRequest } from "next/server";
import { ApiResponseHandler } from "@/lib/api/response";
import { StrategyTradeDetailsService } from "../services";

interface RouteContext {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, context: RouteContext) {
  try {
    const id = parseInt(context.params.id);

    if (isNaN(id)) {
      return ApiResponseHandler.error("无效的ID参数", 400);
    }

    const tradeDetail = await StrategyTradeDetailsService.getTradeDetailById(
      id
    );

    if (!tradeDetail) {
      return ApiResponseHandler.error("未找到指定的交易详情", 404);
    }

    return ApiResponseHandler.success(tradeDetail, "查询成功");
  } catch (error) {
    console.error(`查询策略交易详情失败 (ID: ${context.params.id}):`, error);
    const errorMessage = error instanceof Error ? error.message : "查询失败";
    return ApiResponseHandler.error(errorMessage, 500);
  }
}
