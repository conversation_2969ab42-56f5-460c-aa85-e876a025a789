import React, { useState, useEffect } from "react";
import { Button, Modal, Table, Tag, Tooltip, message } from "antd";
import type { ColumnsType } from "antd/es/table";
import { getFeatures } from "../services/taskAnalysisService";
import { CopyOutlined } from "@ant-design/icons";

interface FeatureDetailModalProps {
  taskId?: number;
  visible: boolean;
  onCancel: () => void;
}

export interface Feature {
  id: number;
  task_id: number;
  feature_code: string;
  feature_name: string;
  formula: string;
  description: string;
  created_at: string;
}

const FeatureDetailModal: React.FC<FeatureDetailModalProps> = ({
  taskId,
  visible,
  onCancel,
}) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<Feature[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 100,
    total: 0,
  });

  const handleCopyFormula = (text: string) => {
    navigator.clipboard.writeText(text);
    message.success("已复制公式");
  };

  const columns: ColumnsType<Feature> = [
    {
      title: "特征名称",
      dataIndex: "feature_name",
      key: "feature_name",
    },
    {
      title: "特征代码",
      dataIndex: "feature_code",
      key: "feature_code",
    },
    {
      title: "计算公式",
      dataIndex: "formula",
      key: "formula",
      render: (text: string) => (
        <>
          {text}
          <Button
            type="text"
            icon={<CopyOutlined />}
            onClick={() => handleCopyFormula(text)}
          />
        </>
      ),
    },
    {
      title: "描述",
      dataIndex: "description",
      key: "description",
    },
  ];

  const fetchData = async () => {
    if (!taskId) return;
    try {
      setLoading(true);
      const result = await getFeatures({
        task_id: taskId,
        page: pagination.current,
        pageSize: pagination.pageSize,
      });
      setData(result.data);
      setPagination({
        ...pagination,
        total: result.pagination.total || 0,
      });
    } catch (error) {
      message.error("获取特征数据失败");
      console.error("获取特征数据失败:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchData();
    }
  }, [visible, pagination.current, pagination.pageSize]);

  return (
    <>
      <Modal
        title="特征详情"
        width={1200}
        open={visible}
        onCancel={onCancel}
        footer={null}
        destroyOnClose
      >
        <Table
          sticky
          scroll={{ x: "max-content" }}
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: (page, pageSize) => {
              setPagination({
                current: page,
                pageSize,
                total: pagination.total,
              });
            },
          }}
        />
      </Modal>
    </>
  );
};

export default FeatureDetailModal;
