import { NextRequest } from 'next/server';
import { ApiResponseHandler } from '@/lib/api/response';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const type = searchParams.get('type') as 'stock' | 'fund' | 'index' | undefined;

    // 模拟热门股票数据，实际项目中应该从数据库或外部API获取
    const hotStocksData = [
      {
        code: '600519',
        name: '贵州茅台',
        market: 'SH',
        type: 'stock',
        industry: '白酒',
        price: 1680.00,
        change: -15.20,
        change_percent: -0.90,
        volume: 1250000,
        turnover: 2100000000,
        heat_score: 98.5, // 热度评分
        reason: '白酒龙头，业绩稳定增长',
      },
      {
        code: '300750',
        name: '宁德时代',
        market: 'SZ',
        type: 'stock',
        industry: '新能源',
        price: 185.20,
        change: 8.50,
        change_percent: 4.81,
        volume: 8500000,
        turnover: 1575000000,
        heat_score: 96.8,
        reason: '新能源汽车产业链核心',
      },
      {
        code: '000858',
        name: '五粮液',
        market: 'SZ',
        type: 'stock',
        industry: '白酒',
        price: 128.50,
        change: -2.30,
        change_percent: -1.76,
        volume: 3200000,
        turnover: 411200000,
        heat_score: 94.2,
        reason: '白酒板块优质标的',
      },
      {
        code: '600036',
        name: '招商银行',
        market: 'SH',
        type: 'stock',
        industry: '银行',
        price: 35.80,
        change: 0.65,
        change_percent: 1.85,
        volume: 15600000,
        turnover: 558480000,
        heat_score: 92.1,
        reason: '零售银行龙头',
      },
      {
        code: '002415',
        name: '海康威视',
        market: 'SZ',
        type: 'stock',
        industry: '安防',
        price: 28.90,
        change: 1.20,
        change_percent: 4.33,
        volume: 12800000,
        turnover: 369920000,
        heat_score: 90.5,
        reason: '安防龙头，AI技术领先',
      },
      {
        code: '688981',
        name: '中芯国际',
        market: 'SH',
        type: 'stock',
        industry: '半导体',
        price: 45.80,
        change: 2.10,
        change_percent: 4.81,
        volume: 8900000,
        turnover: 407620000,
        heat_score: 89.3,
        reason: '半导体制造龙头',
      },
      {
        code: '300059',
        name: '东方财富',
        market: 'SZ',
        type: 'stock',
        industry: '互联网金融',
        price: 15.60,
        change: -0.45,
        change_percent: -2.80,
        volume: 25600000,
        turnover: 399360000,
        heat_score: 87.8,
        reason: '互联网券商龙头',
      },
      {
        code: '600887',
        name: '伊利股份',
        market: 'SH',
        type: 'stock',
        industry: '食品饮料',
        price: 32.40,
        change: 0.80,
        change_percent: 2.53,
        volume: 6800000,
        turnover: 220320000,
        heat_score: 85.6,
        reason: '乳制品行业龙头',
      },
      {
        code: '00700',
        name: '腾讯控股',
        market: 'HK',
        type: 'stock',
        industry: '互联网',
        price: 320.00,
        change: -8.50,
        change_percent: -2.59,
        volume: 18500000,
        turnover: 5920000000,
        heat_score: 95.2,
        reason: '互联网巨头，游戏社交双龙头',
      },
      {
        code: '09988',
        name: '阿里巴巴-SW',
        market: 'HK',
        type: 'stock',
        industry: '互联网',
        price: 85.50,
        change: 2.30,
        change_percent: 2.76,
        volume: 22000000,
        turnover: 1881000000,
        heat_score: 93.7,
        reason: '电商巨头，云计算业务增长强劲',
      },
      // 指数
      {
        code: '000001',
        name: '上证指数',
        market: 'SH',
        type: 'index',
        industry: '指数',
        price: 3245.67,
        change: -15.23,
        change_percent: -0.47,
        volume: 0,
        turnover: 0,
        heat_score: 88.5,
        reason: 'A股市场风向标',
      },
      {
        code: '399006',
        name: '创业板指',
        market: 'SZ',
        type: 'index',
        industry: '指数',
        price: 2156.34,
        change: 12.45,
        change_percent: 0.58,
        volume: 0,
        turnover: 0,
        heat_score: 86.2,
        reason: '成长股集中地',
      },
      // 基金ETF
      {
        code: '510050',
        name: '50ETF',
        market: 'SH',
        type: 'fund',
        industry: 'ETF',
        price: 2.85,
        change: -0.02,
        change_percent: -0.70,
        volume: 45600000,
        turnover: 129960000,
        heat_score: 82.1,
        reason: '蓝筹股ETF代表',
      },
      {
        code: '510300',
        name: '300ETF',
        market: 'SH',
        type: 'fund',
        industry: 'ETF',
        price: 4.20,
        change: 0.05,
        change_percent: 1.20,
        volume: 38900000,
        turnover: 163380000,
        heat_score: 80.8,
        reason: '沪深300指数基金',
      },
    ];

    // 根据类型过滤
    let filteredStocks = hotStocksData;
    if (type) {
      filteredStocks = hotStocksData.filter(stock => stock.type === type);
    }

    // 按热度评分排序
    filteredStocks.sort((a, b) => b.heat_score - a.heat_score);

    // 限制返回数量
    const result = filteredStocks.slice(0, limit);

    // 添加排名信息
    const rankedResult = result.map((stock, index) => ({
      ...stock,
      rank: index + 1,
      timestamp: Date.now(),
    }));

    return ApiResponseHandler.success(rankedResult);

  } catch (error) {
    console.error('获取热门股票失败:', error);
    return ApiResponseHandler.error('服务器内部错误');
  }
}

// 获取涨跌幅排行
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      sort_by = 'change_percent', // 排序字段: change_percent, volume, turnover
      order = 'desc', // 排序方向: asc, desc
      market, // 市场筛选: SH, SZ, HK
      type, // 类型筛选: stock, fund, index
      limit = 50 
    } = body;

    // 模拟更多股票数据用于排行榜
    const allStocksData = [
      // 这里可以包含更多股票数据
      ...hotStocksData, // 复用上面的数据
      // 可以添加更多模拟数据
    ];

    let filteredStocks = allStocksData;

    // 按市场过滤
    if (market) {
      filteredStocks = filteredStocks.filter(stock => stock.market === market);
    }

    // 按类型过滤
    if (type) {
      filteredStocks = filteredStocks.filter(stock => stock.type === type);
    }

    // 排序
    filteredStocks.sort((a, b) => {
      const aValue = a[sort_by as keyof typeof a] as number;
      const bValue = b[sort_by as keyof typeof b] as number;
      
      if (order === 'desc') {
        return bValue - aValue;
      } else {
        return aValue - bValue;
      }
    });

    // 限制返回数量并添加排名
    const result = filteredStocks.slice(0, limit).map((stock, index) => ({
      ...stock,
      rank: index + 1,
      timestamp: Date.now(),
    }));

    return ApiResponseHandler.success({
      list: result,
      total: filteredStocks.length,
      sort_by,
      order,
      market,
      type,
    });

  } catch (error) {
    console.error('获取排行榜失败:', error);
    return ApiResponseHandler.error('服务器内部错误');
  }
}

// 模拟热门股票数据
const hotStocksData = [
  {
    code: '600519',
    name: '贵州茅台',
    market: 'SH',
    type: 'stock',
    industry: '白酒',
    price: 1680.00,
    change: -15.20,
    change_percent: -0.90,
    volume: 1250000,
    turnover: 2100000000,
    heat_score: 98.5,
    reason: '白酒龙头，业绩稳定增长',
  },
  // ... 其他数据
] as const;
