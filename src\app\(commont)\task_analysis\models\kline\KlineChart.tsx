"use client";

import React, { useEffect, useRef } from "react";
import {
  init,
  dispose,
  Chart,
  Options,
  LayoutChildType,
  registerIndicator,
} from "klinecharts";
import type { DeepPartial, KLineData } from "klinecharts";
import type { Styles, LineType, PanePosition } from "klinecharts";
import { useTheme } from "@/contexts/ThemeContext";
import ModelForecast from "./Indicators/ModelForecast";
import { themeColors } from "@/app/constants";

export interface MainKLineData {
  t: number; // 时间戳（Unix timestamp）
  o: number; // 开盘价
  h: number; // 最高价
  l: number; // 最低价
  c: number; // 收盘价
  v: number; // 成交量
}

export interface UpDownData {
  t: number; // 时间戳（与K线数据对齐）
  up: number; // 上涨比例（%）
  down: number; // 下跌比例（%）
}

export interface ChartData {
  main: MainKLineData[];
}

const initOptions: Options = {
  locale: "zh-CN",
  layout: [
    {
      type: "candle" as LayoutChildType,
      options: {
        id: "main-chart",
      },
    },
    {
      type: "indicator" as LayoutChildType,
      options: {
        id: "indicator",
        height: 100,
      },
    },
    {
      type: "xAxis" as LayoutChildType,
      options: {
        position: "bottom" as PanePosition,
      },
    },
  ],
};

export default function KlineChart({ main,targetType }: ChartData&{
  targetType: number;
}) {
  const chartRef = useRef<HTMLDivElement>(null);
  const mainChartRef = useRef<Chart | null>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);
  const { isDark } = useTheme();

  const styles: DeepPartial<Styles> = {
    grid: {
      show: true,
      horizontal: {
        show: true,
        size: 1,
        color: isDark ? themeColors.accent[100] : "#f0f0f0",
        style: "solid" as LineType,
        dashedValue: [4, 4],
      },
      vertical: {
        show: true,
        size: 1,
        color:  isDark ? themeColors.accent[100]: "#f0f0f0",
        style: "solid" as LineType,
        dashedValue: [4, 4],
      },
    },
    xAxis: {
      show: true,
      axisLine: {
        show: true,
        color: isDark ? "#333333" : "#d9d9d9",
        size: 1,
      },
      tickText: {
        show: true,
        color: isDark ? "#a0a0a0" : "#666666",
        size: 11,
        family: "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
        weight: "500",
      },
      tickLine: {
        show: true,
        size: 1,
        length: 4,
        color: isDark ? "#333333" : "#d9d9d9",
      },
    },
    yAxis: {
      show: true,
      inside: true,
      axisLine: {
        show: true,
        color: isDark ? "#333333" : "#d9d9d9",
        size: 1,
      },
      tickText: {
        show: true,
        color: isDark ? "#a0a0a0" : "#666666",
        size: 11,
        family: "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
        weight: "500",
      },
      tickLine: {
        show: true,
        size: 1,
        length: 4,
        color: isDark ? "#333333" : "#d9d9d9",
      },
    },
    candle: {
      bar: {
        upColor: isDark ? "#089981" : "#00e6b8",
        downColor: isDark ? "#F23645" : "#ff6666",
        noChangeColor: isDark ? "#a6a6a6" : "#999999",
        upBorderColor: isDark ? "#089981" : "#00e6b8",
        downBorderColor: isDark ? "#F23645" : "#ff6666",
        noChangeBorderColor: isDark ? "#a6a6a6" : "#999999",
        upWickColor: isDark ? "#089981" : "#00e6b8",
        downWickColor: isDark ? "#F23645" : "#ff6666",
        noChangeWickColor: isDark ? "#a6a6a6" : "#999999",
      },
      tooltip: {
        text: {
          color: isDark ? "#ffffff" : "#333333",
          size: 12,
          family: "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
          weight: "400",
        },
      },
    },
    indicator: {
      tooltip: {
        text: {
          color: isDark ? "#ffffff" : "#333333",
          size: 12,
          family: "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
          weight: "400",
        },
      },
    },
    crosshair: {
      show: true,
      horizontal: {
        show: true,
        line: {
          show: true,
          style: "dashed" as LineType,
          dashedValue: [4, 4],
          size: 1,
          color: isDark ? "#4a9eff" : "#1890ff",
        },
        text: {
          show: true,
          color: "#ffffff",
          size: 11,
          family: "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
          weight: "500",
          paddingLeft: 8,
          paddingRight: 8,
          paddingTop: 4,
          paddingBottom: 4,
          borderRadius: 4,
          backgroundColor: isDark ? "#4a9eff" : "#1890ff",
        },
      },
      vertical: {
        show: true,
        line: {
          show: true,
          style: "dashed" as LineType,
          dashedValue: [4, 4],
          size: 1,
          color: isDark ? "#4a9eff" : "#1890ff",
        },
        text: {
          show: true,
          color: "#ffffff",
          size: 11,
          family: "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
          weight: "500",
          paddingLeft: 8,
          paddingRight: 8,
          paddingTop: 4,
          paddingBottom: 4,
          borderRadius: 4,
          backgroundColor: isDark ? "#4a9eff" : "#1890ff",
        },
      },
    },
  };

  // 初始化图表
  useEffect(() => {
    if (!chartRef.current) return;

    // 处理数据
    const klineData: KLineData[] = main.map((item) => ({
      ...item,
      timestamp: item.t,
      open: item.o,
      high: item.h,
      low: item.l,
      close: item.c,
      volume: item.v,
      value: item.v, // 这里的value可以是成交量或其他值，取决于你的需求,
    }));



    // 创建图表
    mainChartRef.current = init(chartRef.current, initOptions);
    mainChartRef.current?.applyNewData(klineData);
    mainChartRef.current?.setStyles(styles);

    // 创建自定义指标
    registerIndicator(ModelForecast(targetType));
    mainChartRef.current?.createIndicator("forecast_modal", false, {
      id: "indicator",
      height: 100,
    });

    return () => {
      if (mainChartRef.current) {
        dispose(mainChartRef.current);
      }
    };
  }, [styles, main]);

  // 监听容器大小变化
  useEffect(() => {
    const resizeCallback = () => {
      if (mainChartRef.current) {
        mainChartRef.current.resize();
      }
    };

    resizeObserverRef.current = new ResizeObserver(resizeCallback);

    if (chartRef.current) {
      resizeObserverRef.current.observe(chartRef.current);
    }

    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, []);

  return (
    <div className="w-full h-[600px] rounded-lg overflow-hidden shadow-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-black">
      <div ref={chartRef} className="w-full h-full" />
    </div>
  );
}
