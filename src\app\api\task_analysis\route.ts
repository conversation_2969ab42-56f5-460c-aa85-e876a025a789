import { NextResponse } from "next/server";
import { ApiResponseHandler } from "@/lib/api/response";
import { TaskAnalysisRepository } from "./repositories";
import { TaskDataType } from "@/app/(commont)/task_analysis/services/taskAnalysisService";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const tasks = await TaskAnalysisRepository.getTasks(page, pageSize);
    return ApiResponseHandler.success(tasks);
  } catch (error) {
    console.error("获取分析任务列表失败:", error);
    return ApiResponseHandler.error("获取分析任务列表失败");
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = parseInt(searchParams.get("id") || "0");
    await TaskAnalysisRepository.deleteTask(id);
    return ApiResponseHandler.success(null);
  } catch (error) {
    console.error("删除分析任务失败:", error);
    return ApiResponseHandler.error("删除分析任务失败");
  }
}

export async function POST(request: Request) {
  try {
    const taskData: TaskDataType = await request.json();
    const newTask = await TaskAnalysisRepository.createTask(taskData);
    return ApiResponseHandler.success({},'添加分析任务成功');
  } catch (error) {
    console.error("创建分析任务失败:", error);
    return ApiResponseHandler.error("创建分析任务失败");
  }
}

export async function PUT(request: Request) {
  try {
    const taskData: TaskDataType = await request.json();
    const updatedTask = await TaskAnalysisRepository.updateTask(taskData);
    return ApiResponseHandler.success({},'更新分析任务成功');
  } catch (error) {
    console.error("更新分析任务失败:", error);
    return ApiResponseHandler.error("更新分析任务失败");
  }
}
