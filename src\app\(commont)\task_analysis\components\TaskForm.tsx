"use client";

import React, { use, useEffect } from "react";
import {
  Modal,
  Form,
  Button,
  Checkbox,
  Select,
  Col,
  Row,
  Radio,
  FormInstance,
  DatePicker,
} from "antd";
import {
  StockTarget,
  TaskDataType,
  UpdateTaskDataType,
} from "../services/taskAnalysisService";
import { STOCK_TYPE } from "@/constants";

interface TaskFormProps {
  open: boolean;
  onCancel: () => void;
  onSubmit: (values: TaskDataType | UpdateTaskDataType) => Promise<void>;
  targetOptions: { label: string; value: number }[];
  modelOptions: any[];
  options: StockTarget[];
  initialValues?: TaskDataType;
  form: FormInstance;
  searchTarget?: (target: string) => Promise<void>;
}

const TaskForm: React.FC<TaskFormProps> = ({
  open,
  onCancel,
  onSubmit,
  targetOptions,
  modelOptions,
  options,
  form,
  searchTarget
}) => {
  React.useEffect(() => {
    console.log(form.getFieldValue("code"));
    const type =
      Object.keys(form.getFieldValue("code") ?? {}).length > 0
        ? Object.keys(form.getFieldValue("code") ?? {})[0]
        : undefined;
    form.setFieldsValue({
      type,
    });
  }, [open]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log(values);
      // 处理数据
      const postData: TaskDataType = {
        market_ids: (Object.values(values?.code)?.flat() ?? [])
          .filter(Boolean)
          .join(","),
        models: values.models ?? [],
        analysis_target: values.analysis_target,
        analysis_purpost: values.analysis_purpost,
        technical_indicators: values.technical_indicators,
        id: values?.id ? values?.id : undefined,
        train_start_time: values?.training_period[0]?.format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        train_end_time: values?.training_period[1]?.format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        test_start_time: values?.testing_period[0]?.format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        test_end_time: values?.testing_period[1]?.format("YYYY-MM-DD HH:mm:ss"),
        expert_num: values?.expert_num,
        analysis_period: values?.analysis_period,
      };

      await onSubmit(postData);
      form.resetFields();
    } catch (error) {
      console.error("表单验证失败:", error);
    }
  };
  return (
    <Modal
      title="添加分析任务"
      open={open}
      destroyOnClose
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          提交
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical" name="taskForm">
        <Form.Item name="id" label="用于记录id不展示" hidden />

        <Form.Item
          name="analysis_target"
          label="分析目标"
          rules={[{ required: true, message: "请选择分析目标" }]}
        >
          <Select
            placeholder="请选择分析目标"
            options={targetOptions}
            showSearch
            filterOption={(input, option) =>
              (option?.label?? "")
               .toLowerCase()
               .includes(input.toLowerCase())
            }
          />
        </Form.Item>

        <Form.Item
          name="analysis_purpost"
          label="分析目的"
          initialValue={1}
          rules={[{ required: true, message: "请输入任务标题" }]}
        >
          <Radio.Group options={[{ label: "涨跌概率", value: 1 }]} />
        </Form.Item>

        <Form.Item label="分析周期" name="analysis_period" initialValue={5}>
          <Radio.Group
            options={[
              {
                label: "5分钟",
                value: 5,
              },
              {
                label: "1小时",
                value: 60,
              },
              {
                label: "1天",
                value: 1440,
              },
            ]}
          />
        </Form.Item>

        <Form.Item name="models" label="可用模型">
          <Checkbox.Group>
            <Row gutter={[16, 8]}>
              {modelOptions.map((option) => (
                <Col span={8} key={option.value}>
                  <Checkbox value={option.value}>{option.label}</Checkbox>
                </Col>
              ))}
            </Row>
          </Checkbox.Group>
        </Form.Item>

        <Form.Item
          label="可用数据"
          shouldUpdate
          rules={[{ required: true, message: "请选择使用的数据" }]}
        >
          {({ getFieldValue }) => (
            <>
              <Form.Item
                name="type"
                rules={[{ required: true, message: "请选择股票代码" }]}
                noStyle={!getFieldValue("type")}
              >
                <Radio.Group
                  optionType="button"
                  buttonStyle="solid"
                  options={Object.entries(STOCK_TYPE).map(([key, value]) => {
                    return {
                      label: value,
                      value: key,
                    };
                  })}
                />
              </Form.Item>
              <Form.Item
                dependencies={["type"]}
                hidden={!getFieldValue("type")}
                noStyle
              >
                {({ getFieldValue }) =>
                  Object.entries(STOCK_TYPE)
                    .map(([key, value]) => {
                      return {
                        label: value,
                        value: key,
                      };
                    })
                    .map(({ value }) => {
                      return (
                        <Form.Item
                          name={["code", value]}
                          dependencies={["type"]}
                          noStyle
                          rules={
                            getFieldValue("type") === value
                              ? [
                                  {
                                    validator: () => {
                                      if (
                                        Object.values(
                                          getFieldValue(["code"]) ?? {}
                                        )?.flat()?.length === 0
                                      ) {
                                        return Promise.reject(
                                          new Error("请选择股票代码")
                                        );
                                      } else {
                                        return Promise.resolve();
                                      }
                                    },
                                  },
                                ]
                              : []
                          }
                          hidden={getFieldValue("type") !== value}
                        >
                          <Select
                            placeholder="请选择股票代码"
                            showSearch
                            filterOption={(input, option) =>
                              (option?.label ?? "")
                                .toLowerCase()
                                .includes(input.toLowerCase())
                            }
                            mode="multiple"
                            options={options
                              ?.filter(
                                (item) =>
                                  item.type === form.getFieldValue(["type"])
                              )
                              .map((item) => ({
                                value: item.id,
                                label: item.name,
                              }))}
                          />
                        </Form.Item>
                      );
                    })
                }
              </Form.Item>
            </>
          )}
        </Form.Item>

        <Form.Item
          label="训练时间"
          name="training_period"
          rules={[{ required: true, message: "请选择训练时间范围" }]}
        >
          <DatePicker.RangePicker showTime />
        </Form.Item>
        <Form.Item
          label="测试时间"
          name="testing_period"
          rules={[{ required: true, message: "请选择测试时间范围" }]}
        >
          <DatePicker.RangePicker showTime />
        </Form.Item>

        {/* <Form.Item label="可用指标" required>
          <Tabs
            defaultActiveKey="趋势类指标"
            items={Object.entries(indicatorData).map(
              ([category, indicators]) => ({
                key: category,
                label: category,
                children: (
                  <Form.Item
                    name="technical_indicators"
                    noStyle
                    rules={[{ required: true, message: "请选择使用的指标" }]}
                  >
                    <Checkbox.Group
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "8px",
                      }}
                    >
                      {indicators.map((indicator: IndicatorData) => (
                        <Tooltip
                          key={indicator.name}
                          title={
                            <div>
                              {indicator.description && (
                                <div>{indicator.description}</div>
                              )}
                              {indicator.function && (
                                <div>功能：{indicator.function}</div>
                              )}
                              {indicator.purpose && (
                                <div>用途：{indicator.purpose}</div>
                              )}
                              {indicator.types && (
                                <div>类型：{indicator.types.join(", ")}</div>
                              )}
                            </div>
                          }
                        >
                          <Checkbox value={indicator.name}>
                            {indicator.name}
                          </Checkbox>
                        </Tooltip>
                      ))}
                    </Checkbox.Group>
                  </Form.Item>
                ),
              })
            )}
          />
        </Form.Item> 

        <Form.Item
          name="expert_num"
          label="专家数量"
          colon={false}
          layout="horizontal"
        >
          <InputNumber
            placeholder="请输入专家数量"
            style={{ width: 150 }}
            controls={false}
          />
        </Form.Item> */}
      </Form>
    </Modal>
  );
};

export default TaskForm;
