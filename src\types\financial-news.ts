/**
 * 财经新闻数据接口
 */
export interface FinancialNews {
  id: number;
  title: string;
  summary: string;
  type: 1 | 2;
  publish_time: string;
  link: string;
  created_at: string;
  sentiment: "positive" | "negative" | "neutral" | null;
  impact_level: number | null;
  stock_codes: string | null;
  analysis_result: string | null;
  analyzed_at: string | null;
  analysis_status: number;
}

/**
 * 财经新闻查询参数
 */
export interface FinancialNewsQuery {
  page: number;
  pageSize: number;
  keyword?: string;
  sentiment?: string;
  impact_level?: number | "";
  analysis_status?: number;
  startDate?: string;
  endDate?: string;
  sortField?: string;
  sortOrder?: "asc" | "desc";
  type?: 1 | 2;
  stock_codes?: string;
}

/**
 * 财经新闻分页响应
 */
export interface FinancialNewsResponse {
  data: FinancialNews[];
  total: number;
  page: number;
  pageSize: number;
}

export interface FinancialNewsStockDataType {
  tdate: string;
  price: number;
  zdfd: number;
  zded: number;
  cjl: number;
  cje: number;
  hslv: number;
  name: string;
}

export interface FinancialNewsStockKLineDataType {
  code: string;
  name: string;
  ktype: number;
  tdate: string;
  open: number;
  close: number;
  high: number;
  low: number;
  cjl: number;
  cje: number;
}
