import db from "@/lib/quantDB";

export class StrategyRepository {
  static async getDateRangesByBacktestId(
    strategy_name: string,
    entry_conditions: string
  ) {
    const [rows] = await db.query(
      "select start_date, end_date,backtest_id from strategy_backtest_summary where strategy_name = ? and entry_conditions = ? group by start_date, end_date,backtest_id;",
      [strategy_name, entry_conditions]
    );
    // 将查询结果转换为数组并返回第一个元素
    return rows;
  }

  static async getTradeStatsAllSections(backtest_id: string) {
    const [rows] = await db.query(
      `select t.stock_code, t.stock_name, count(*) as total_num,ROUND(
		(SUM(CASE WHEN t.profit_pct > 0 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)),2) 
    AS win_rate from strategy_trades as t where backtest_id = ? group by t.stock_code, t.stock_name
`,
      [backtest_id]
    );
    return rows;
  }
  static async getTradeStatsSpecifiedSection(backtest_id: string,section: string) {
    const [rows] = await db.query(
      `select t.stock_code, t.stock_name, count(*) as total_num,ROUND(

		(SUM(CASE WHEN t.profit_pct > 0 THEN 1 ELSE 0 END) * 100.0 / COUNT(*)),

		2

) AS win_rate from strategy_trades as t 

left join stock_basic_info as i on i.stock_code = t.stock_code

where i.industry = ? and backtest_id = ? group by t.stock_code, t.stock_name
`,
      [section,backtest_id]
    );
    return rows;
  }

  static async getTradeDetails(backtest_id: string, stock_code: string) {
    const [rows] = await db.query(
      "SELECT stock_code, stock_name, entry_date, entry_price, exit_date, exit_price, profit_pct, indicators FROM strategy_trades WHERE backtest_id = ? AND stock_code = ?",
      [backtest_id, stock_code]
    );
    return rows;
  }

  static async getStrategyStats(backtest_id: string) {
    const [rows] = await db.query(
      "SELECT * FROM strategy_backtest_summary WHERE backtest_id = ?",
      [backtest_id]
    );
    return (rows as any[])[0];
  }
}
