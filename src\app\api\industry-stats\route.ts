import { NextResponse } from "next/server";
import { getIndustryStats } from "./repositories";
import { ApiResponseHandler } from "@/lib/api/response";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const backtestId = searchParams.get("backtestId");

  if (!backtestId) {
    return ApiResponseHandler.error("缺少必要参数: backtestId", 400);
  }

  try {
    const data = await getIndustryStats(backtestId);
    return ApiResponseHandler.success(data);
  } catch (error) {
    console.error("获取板块统计数据失败:", error);
    return ApiResponseHandler.error("获取板块统计数据失败");
  }
}
