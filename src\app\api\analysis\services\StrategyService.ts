import { StrategyRepository } from '../repository/StrategyRepository'

export class StrategyService {
  static async getDateRanges(strategy_name: string, entry_conditions: string) {
    return await StrategyRepository.getDateRangesByBacktestId(strategy_name, entry_conditions)
  }

  static async getStrategyStats(backtest_id: string) {
    return await StrategyRepository.getStrategyStats(backtest_id)
  }

}
