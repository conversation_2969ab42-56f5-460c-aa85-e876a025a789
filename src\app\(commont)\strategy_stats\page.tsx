"use client";

import {
  <PERSON>,
  <PERSON>lt<PERSON>,
  Divider,
  But<PERSON>,
  Flex,
  Form,
  Grid,
  message,
} from "antd";
import { useState, useEffect, useMemo, useCallback } from "react";
import type { StrategyStats } from "./types";
import {
  CloudFilled,
  QuestionCircleOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
} from "@ant-design/icons";
import { strategyStatsService } from "./services/StrategyStatsServices";
import moment from "moment";
import { ColumnsType } from "antd/es/table";
import Search from "./components/search";
import StrategyDetailModal from "./components/StrategyDetailModal";

// 导入新组件
import IndustryStatsModal from "./components/IndustryStatsModal";
import { omit } from "lodash";
import { formatPeriod, getMarketTypeLabel } from "./constants";
import { strategyConfigsService } from "./services/StrategyConfigsService";
import { StrategyConfig } from "@/types/strategy-config";
import Star from "@/component/Star";

export interface FetchDataParams {
  page?: number;
  pageSize?: number;
  orderBy?: string;
  order?: "ASC" | "DESC";
  strategyName?: string;
  testBatchId?: string;
  marketType?: string;
  period?: number;
  star?: 1 | 0;
  // 其他搜索条件
  moreSearch?: string;
}

export default function StrategyStats() {
  const [form] = Form.useForm();
  const [data, setData] = useState<StrategyStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 100,
    total: 0,
  });
  const [sortField, setSortField] = useState<{
    orderBy?: string;
    order?: "ASC" | "DESC";
  }>({});
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedStrategyRecord, setSelectedStrategyRecord] =
    useState<StrategyStats | null>(null);
  const [filterConditions, setFilterConditions] = useState<string>();
  const [allStrategyParams, setAllStrategyParams] = useState<StrategyConfig[]>(
    []
  );
  const screens = Grid.useBreakpoint();

  const fetchData = async (params?: FetchDataParams) => {
    try {
      setLoading(true);
      const formValues = form.getFieldsValue();

      const res = await strategyStatsService.getStrategyStats({
        page: params?.page ?? pagination.current,
        pageSize: params?.pageSize ?? pagination.pageSize,
        orderBy: params?.orderBy,
        order: params?.order,
        strategyName: params?.strategyName ?? formValues.strategyName,
        testBatchId: params?.testBatchId ?? formValues.testBatchId,
        marketType: params?.marketType ?? formValues.marketType,
        period: params?.period ?? formValues.period,
        star: params?.star ?? formValues.star, // 星标状态
        // 其他搜索条件
        moreSearch: params?.moreSearch,
      });

      setData(res.data);
      setPagination(res.pagination);
    } catch (error) {
      console.error("获取数据失败:", error);
    } finally {
      setLoading(false);
    }
  };

  const getAllStrategyParams = useCallback(async () => {
    try {
      const response = await strategyConfigsService.getStrategyConfigs();
      setAllStrategyParams(response.data);
    } catch (error) {
      console.error("获取策略参数失败:", error);
      return [];
    }
  }, []);

  // 共享的星标操作处理
  const handleStarChange = useCallback(
    async (record: StrategyStats, checked: boolean) => {
      try {
        await strategyStatsService.updateStar(record.id, checked);
        await fetchData();
      } catch (error) {
        console.error("更新星标失败:", error);
      }
    },
    []
  );

  // 共享的批次操作处理
  const handleBatchAction = useCallback(
    async (record: StrategyStats, isReset: boolean = false) => {
      if (isReset) {
        await fetchData({ testBatchId: "" });
        setFilterConditions("");
      } else {
        await fetchData({ testBatchId: record.test_batch_id });
        setFilterConditions(record.test_batch_id);
      }
    },
    []
  );

  // 获取共享的列定义（除了前几列）
  const getSharedColumns = useCallback(
    (): ColumnsType<StrategyStats> => [
      {
        title: "交易次数",
        dataIndex: "total_trades",
        key: "total_trades",
        width: 80,
        sorter: (a: any, b: any) => a.total_trades - b.total_trades,
        render: (text: number) => (
          <span className="font-semibold text-blue-600">
            {text?.toLocaleString() || 0}
          </span>
        ),
      },
      {
        title: "胜率",
        dataIndex: "overall_win_rate",
        key: "overall_win_rate",
        width: 100,
        sorter: (a: any, b: any) => a.overall_win_rate - b.overall_win_rate,
        render: (text: number) => {
          const value = Number(text) || 0;
          const percentage = value > 1 ? value : value * 100;

          return (
            <div className="flex items-center gap-2">
              <span
                className={`${
                  percentage >= 60
                    ? "text-green-600"
                    : percentage >= 50
                    ? "text-green-400"
                    : "text-red-500"
                } font-semibold text-sm`}
              >
                {percentage.toFixed(1)}%
              </span>
              <div className="w-8 h-1.5 bg-gray-200 rounded-full">
                <div
                  className={`h-full rounded-full ${
                    percentage >= 60
                      ? "bg-green-500"
                      : percentage >= 50
                      ? "bg-green-400"
                      : "bg-red-400"
                  }`}
                  style={{ width: `${Math.min(percentage, 100)}%` }}
                />
              </div>
            </div>
          );
        },
      },
      {
        title: "盈利/亏损次数",
        key: "win_lose_trades",
        width: 90,
        render: (_, record) => (
          <div className="flex flex-col gap-1">
            <span className="font-medium text-xs">
              盈:
              <span className="text-green-600 font-medium text-xs">
                {record.total_won_trades?.toLocaleString() || 0}
              </span>
            </span>
            <span className="font-medium text-xs">
              亏:
              <span className="text-red-500 font-medium text-xs">
                {record.total_lost_trades?.toLocaleString() || 0}
              </span>
            </span>
          </div>
        ),
      },
      {
        title: "持仓周期",
        dataIndex: "avg_holding_days",
        key: "avg_holding_days",
        width: 80,
        sorter: true,
        render: (text: number) => {
          const value = Number(text) || 0;
          return (
            <span className="font-medium text-sm">{value.toFixed(1)}天</span>
          );
        },
      },
      {
        title: (
          <Tooltip title="总净盈亏 / 平均净盈亏">
            净盈亏 <QuestionCircleOutlined />
          </Tooltip>
        ),
        dataIndex: "total_net_pnl",
        key: "total_net_pnl",
        width: 140,
        sorter: (a: any, b: any) => a.total_net_pnl - b.total_net_pnl,
        render: (text: number, record) => {
          const totalPnl = Number(text) || 0;
          const avgPnl = Number(record.avg_net_pnl) || 0;

          const formatNumber = (num: number) => {
            return num.toFixed(2);
          };

          return (
            <div className="flex flex-col gap-1">
              <span
                className={`${
                  totalPnl >= 0 ? "text-green-600" : "text-red-500"
                } font-semibold text-sm`}
              >
                {totalPnl >= 0 ? "+" : ""}
                {formatNumber(totalPnl)}
              </span>
              <span
                className={`${
                  avgPnl >= 0 ? "text-green-500" : "text-red-400"
                } text-xs`}
              >
                均: {avgPnl >= 0 ? "+" : ""}
                {formatNumber(avgPnl)}
              </span>
            </div>
          );
        },
      },
      {
        title: (
          <Tooltip title="计算公式：总净盈亏 / (持仓天数 × 交易次数)">
            持仓效率 <QuestionCircleOutlined />
          </Tooltip>
        ),
        key: "avg_holding_return",
        width: 100,
        dataIndex: "avg_holding_return",
        sorter: (a: any, b: any) => a.avg_holding_return - b.avg_holding_return,
        render: (value, record) => {
          const efficiency = Number(value);

          const formatNumber = (num: number) => {
            if (Math.abs(num) >= 1000) {
              return num.toLocaleString("zh-CN", {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2,
              });
            }
            return num.toFixed(2);
          };

          return (
            <div className="flex items-center">
              <span
                className={`${
                  efficiency >= 0 ? "text-green-600" : "text-red-500"
                } font-semibold text-sm`}
              >
                {efficiency >= 0 ? "+" : ""}
                {formatNumber(efficiency)}
              </span>
              <span className="text-xs text-gray-400">/天</span>
            </div>
          );
        },
      },
      {
        title: "盈亏比",
        dataIndex: "profit_factor",
        key: "profit_factor",
        width: 90,
        sorter: (a: any, b: any) => a.profit_factor - b.profit_factor,
        render: (text: number) => {
          const value = Number(text) || 0;
          return (
            <div className="flex flex-col items-center gap-1">
              <span
                className={`${
                  value >= 2
                    ? "text-green-600"
                    : value >= 1.5
                    ? "text-green-500"
                    : value >= 1
                    ? "text-yellow-600"
                    : "text-red-500"
                } font-bold text-sm`}
              >
                {value.toFixed(2)}
              </span>
            </div>
          );
        },
      },
      // 操作列
      {
        title: "操作",
        key: "action",
        fixed: "right",
        width: 100,
        render: (_, record) => (
          <div className="flex flex-col gap-1">
            <Button
              type="link"
              size="small"
              onClick={() => {
                setSelectedStrategyRecord(record);
                setModalVisible(true);
              }}
            >
              查看策略详细
            </Button>
            <Button
              type="link"
              size="small"
              onClick={() => {
                window.open(`/strategy_stats/details/${record.id}`, "_blank");
              }}
            >
              查看交易详细
            </Button>
            {/* <Button
              type="link"
              size="small"
              onClick={() => {
                window.open(`/strategy_stats/details/${record.id}`, "_blank");
              }}
            >
              查看真实交易详细
            </Button> */}
          </div>
        ),
      },
    ],
    []
  );

  // 渲染星标组件
  const renderStarComponent = useCallback(
    (record: StrategyStats) => (
      <Star
        width="28px"
        height="28px"
        checked={record.star ? true : false}
        onChange={(checked: boolean) => handleStarChange(record, checked)}
      />
    ),
    [handleStarChange]
  );

  // 渲染批次操作按钮
  const renderBatchActions = useCallback(
    (record: StrategyStats) => (
      <>
        {!!filterConditions ? (
          <Tooltip title="恢复搜索所有" placement="top">
            <ZoomOutOutlined
              className="hover:text-blue-600 cursor-pointer text-xs"
              onClick={async (e) => {
                e.stopPropagation();
                await handleBatchAction(record, true);
              }}
            />
          </Tooltip>
        ) : (
          <Tooltip title="搜索相同批次" placement="top">
            <ZoomInOutlined
              className="hover:text-blue-600 cursor-pointer text-xs"
              onClick={async (e) => {
                e.stopPropagation();
                await handleBatchAction(record, false);
              }}
            />
          </Tooltip>
        )}
      </>
    ),
    [filterConditions, handleBatchAction]
  );

  const getColumns = useCallback((): ColumnsType<StrategyStats> => {
    const sharedColumns = getSharedColumns();

    if (screens.xxl) {
      // 宽屏模式，分离前几列
      const wideScreenColumns: ColumnsType<StrategyStats> = [
        {
          title: "ID",
          key: "id",
          dataIndex: "id",
          width: 80,
          onCell: (record: StrategyStats) => ({
            onDoubleClick: () => {
              navigator.clipboard.writeText(record.id.toString());
              message.success(`ID:${record.id.toString()}已复制`);
            },
          }),
        },
        {
          title: "策略名称",
          dataIndex: "strategy_name",
          key: "strategy_name",
          className: "font-medium",
          width: 140,
          fixed: "left" as const,
          render: (text: string, record: StrategyStats) => (
            <span className="flex items-center gap-1">
              {renderStarComponent(record)}
              <span className="text-xs text-gray-500">{text}</span>
            </span>
          ),
        },
        {
          title: "测试批次ID",
          dataIndex: "test_batch_id",
          key: "test_batch_id",
          width: 180,
          render: (text: string, record: StrategyStats) => (
            <Flex justify="space-between" align="center">
              <div
                onClick={() => {
                  window.open(
                    `/analysis?test_batch_id=${record.test_batch_id}`,
                    "_blank"
                  );
                }}
                className="cursor-pointer flex-1 truncate"
              >
                <Tooltip title={text}>
                  <span className="text-blue-600 hover:text-blue-800 text-xs">
                    {text}
                  </span>
                </Tooltip>
              </div>
              <div className="ml-1">{renderBatchActions(record)}</div>
            </Flex>
          ),
        },
        {
          title: "市场/周期",
          key: "market_period",
          width: 100,
          render: (_: any, record: StrategyStats) => (
            <div className="flex flex-col gap-1">
              <span className="text-xs font-medium">
                {getMarketTypeLabel(record.market_type)}
              </span>
              <span className="text-xs text-gray-500">
                {formatPeriod(record.period)}
              </span>
            </div>
          ),
        },
        {
          title: "回测区间",
          dataIndex: "start_date",
          key: "start_date",
          width: 120,
          render: (text: string, record: StrategyStats) => (
            <div className="flex flex-col gap-1">
              <span className="text-xs">
                {moment(text).format("YYYY-MM-DD")}
              </span>
              <span className="text-xs text-gray-500">
                {moment(record.end_date).format("YYYY-MM-DD")}
              </span>
            </div>
          ),
        },
        ...sharedColumns,
      ];
      return wideScreenColumns;
    } else {
      // 窄屏模式，合并前几列
      const narrowScreenColumns: ColumnsType<StrategyStats> = [
        {
          title: "策略详情",
          key: "strategy_detail",
          width: 200,
          fixed: "left" as const,
          render: (_: any, record: StrategyStats) => (
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-1">
                {renderStarComponent(record)}
                <span className="text-xs text-gray-500">
                  {record.strategy_name}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div
                  onClick={() => {
                    window.open(
                      `/analysis?test_batch_id=${record.test_batch_id}`,
                      "_blank"
                    );
                  }}
                  className="cursor-pointer"
                >
                  <Tooltip title={record.test_batch_id}>
                    <span className="text-blue-600 hover:text-blue-800 text-xs">
                      {record.test_batch_id}
                    </span>
                  </Tooltip>
                </div>
                {renderBatchActions(record)}
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs font-medium">
                  {getMarketTypeLabel(record.market_type)}
                </span>
                <span className="text-xs text-gray-500">
                  {formatPeriod(record.period)}
                </span>
                <span
                  className="text-xs text-gray-500"
                  onDoubleClick={() => {
                    navigator.clipboard.writeText(record.id.toString());
                    message.success("ID:" + record.id.toString() + "已复制");
                  }}
                >
                  ID:{record.id}
                </span>
              </div>
            </div>
          ),
        },
        {
          title: "回测区间",
          dataIndex: "start_date",
          key: "start_date",
          width: 120,
          render: (text: string, record: StrategyStats) => (
            <div className="flex flex-col gap-1">
              <span className="text-xs">
                {moment(text).format("YYYY-MM-DD")}
              </span>
              <span className="text-xs text-gray-500">
                {moment(record.end_date).format("YYYY-MM-DD")}
              </span>
            </div>
          ),
        },
        ...sharedColumns,
      ];
      return narrowScreenColumns;
    }
  }, [
    screens.xxl,
    filterConditions,
    renderStarComponent,
    renderBatchActions,
    getSharedColumns,
  ]);

  const [searchParams, setSearchParams] = useState<{
    name?: string;
    type?: string;
  }>({});

  // 在 handleSearch 中
  const handleSearch = () => {
    fetchData({
      page: 1,
      pageSize: pagination.pageSize,
      orderBy: sortField.orderBy,
      order: sortField.order,
      testBatchId: form.getFieldValue("testBatchId"),
      strategyName: form.getFieldValue("strategyName"),
      marketType: form.getFieldValue("marketType"),
      period: form.getFieldValue("period"),
      star: form.getFieldValue("star"),
      moreSearch: JSON.stringify(
        omit(form.getFieldsValue(), [
          "fields",
          "strategyName",
          "testBatchId",
          "marketType",
          "period",
          "star",
        ])
      ),
    });
  };

  // 在 handleReset 中
  const handleReset = () => {
    setSearchParams({});
    fetchData({ page: 1, pageSize: pagination.pageSize });
  };

  // 在 handleTableChange 中
  const handleTableChange = async (
    pagination: any,
    filters: any,
    sorter: any
  ) => {
    console.log(pagination, filters, sorter);
    await fetchData({
      page: pagination.current,
      pageSize: pagination.pageSize,
      orderBy: sorter.field,
      order: sorter.order === "ascend" ? "ASC" : "DESC",
    });
    setSortField({
      orderBy: sorter.field,
      order: sorter.order === "ascend" ? "ASC" : "DESC",
    });
  };

  useEffect(() => {
    fetchData();
    getAllStrategyParams();
  }, []);

  const [tableScroll, setTableScroll] = useState({ x: 1400, y: 500 });

  const SEARCHABLE_COLUMNS = useMemo(() => {
    const strategy_params: Record<string, any> = data[0]?.strategy_params ?? {};
    const searchableCols: StrategyConfig[] = [];

    allStrategyParams.forEach((param) => {
      if (!param.key) return;
      if (param.key in strategy_params) {
        searchableCols.push(param);
      }
    });

    return searchableCols;
  }, [data, allStrategyParams]);

  useEffect(() => {
    const updateTableScroll = () => {
      const windowHeight = document.documentElement.clientHeight;
      const headerHeight = document.querySelector("form")?.offsetHeight || 0;
      const paginationHeight = 64; // 分页器的高度
      const padding = 48; // 上下边距

      setTableScroll({
        x: 1400,
        y: windowHeight - headerHeight - paginationHeight - padding,
      });
    };

    updateTableScroll();
    window.addEventListener("resize", updateTableScroll);

    return () => window.removeEventListener("resize", updateTableScroll);
  }, []);

  return (
    <>
      <Form form={form}>
        <Search
          searchable_cols={SEARCHABLE_COLUMNS.map((col) => ({
            value: col.key,
            label: col.value,
            option: col.options,
            type: col.type,
          }))}
          searchParams={searchParams}
          setSearchParams={setSearchParams}
          onSearch={handleSearch}
          onReset={handleReset}
        />
      </Form>
      <Table
        sticky
        columns={getColumns()}
        dataSource={data}
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          className: "mt-4",
          pageSizeOptions: ["10", "20", "50", "100", "200", "500"],
        }}
        virtual
        rowKey={(record) => record.id}
        bordered={false}
        scroll={tableScroll}
        onChange={handleTableChange}
        className="w-full"
      />

      <StrategyDetailModal
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setSelectedStrategyRecord(null);
        }}
        record={selectedStrategyRecord}
        columns={SEARCHABLE_COLUMNS}
      />
    </>
  );
}
