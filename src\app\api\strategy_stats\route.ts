import { NextResponse } from "next/server";
import { strategyStatsRepository } from "./repositories/strategyStatsRepositories";
import { ApiResponseHandler } from "@/lib/api/response";

type StrategyStatsRequest = {
  page: number;
  pageSize: number;
  orderBy?: string;
  order?: string;
  strategyName?: string;
  testBatchId?: string;
  marketType?: string;
  period?: number;
  moreSearch?: string;
};

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "100");
    const orderBy = searchParams.get("orderBy") || "created_at";
    const order = searchParams.get("order") || ("DESC" as "ASC" | "DESC");
    const strategyName = searchParams.get("strategyName") || "";
    const testBatchId = searchParams.get("testBatchId") || "";
    const marketType = searchParams.get("marketType") || "";
    const period = searchParams.get("period") ? parseInt(searchParams.get("period")!) : undefined;
    const star = searchParams.get("star") ? parseInt(searchParams.get("star")!) ? 1 : 0 : undefined;
    // 处理 moreSearch 参数，假设它是一个 JSON 字符串
    const moreSearch = searchParams.get("moreSearch") || "";
    

    if (order !== "ASC" && order !== "DESC") {
      return ApiResponseHandler.error("排序方式错误", 400);
    }
    
    const { data, total } = await strategyStatsRepository.getStrategyStats(
      page,
      pageSize,
      order,
      orderBy,
      strategyName,
      testBatchId,
      marketType,
      period,
      star,
      moreSearch ? Object.entries(JSON.parse(moreSearch)) : undefined
    );

    return ApiResponseHandler.success({
      data: data,
      pagination: {
        current: page,
        pageSize,
        total,
      },
    });
  } catch (error) {
    return ApiResponseHandler.error("查询失败"+ error, 500);
  }
}
