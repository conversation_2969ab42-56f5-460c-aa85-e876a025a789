import { ApiResponseHandler } from "@/lib/api/response";
import { getFinancialNews } from "./repositories";
import { FinancialNewsQuery } from "@/types/financial-news";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    
    // 解析查询参数
    const query: FinancialNewsQuery = {
      page: Number(searchParams.get("page")) || 1,
      pageSize: Number(searchParams.get("pageSize")) || 10,
      keyword: searchParams.get("keyword") || undefined,
      sentiment: searchParams.get("sentiment") || undefined,
      impact_level: searchParams.get("impact_level") ? Number(searchParams.get("impact_level")) : undefined,
      startDate: searchParams.get("startDate") || undefined,
      endDate: searchParams.get("endDate") || undefined,
      sortField: searchParams.get("sortField") || "publish_time",
      sortOrder: (searchParams.get("sortOrder") as 'asc' | 'desc') || "desc",
      stock_codes: searchParams.get("stock_codes") || undefined,
      type: searchParams.get("type") ? Number(searchParams.get("type")) as 1 | 2 : undefined,
    };

    // 验证分页参数
    if (query.page < 1 || query.pageSize < 1) {
      return ApiResponseHandler.error("页码和每页大小必须大于0", 400);
    }

    if (query.pageSize > 100) {
      return ApiResponseHandler.error("每页大小不能超过100", 400);
    }

    // 获取数据
    const result = await getFinancialNews(query);
    
    return ApiResponseHandler.success({
      data: result.data,
      total: result.total,
      page: query.page,
      pageSize: query.pageSize,
    });
  } catch (error) {
    console.error("获取财经新闻数据失败:", error);
    return ApiResponseHandler.error("获取财经新闻数据失败", 500);
  }
}

export const dynamic = "force-dynamic";
