import {
  IndicatorTemplate,
  IndicatorFigureStylesCallbackData,
  PolygonType,
  KLineData,
} from "klinecharts";
import { UpDownData } from "../KlineChart";
import { get } from "lodash";

/**
 * MACD：参数快线移动平均、慢线移动平均、移动平均，
 * 默认参数值12、26、9。
 * 公式：⒈首先分别计算出收盘价12日指数平滑移动平均线与26日指数平滑移动平均线，分别记为EMA(12）与EMA(26）。
 * ⒉求这两条指数平滑移动平均线的差，即：DIFF = EMA(SHORT) － EMA(LONG)。
 * ⒊再计算DIFF的M日的平均的指数平滑移动平均线，记为DEA。
 * ⒋最后用DIFF减DEA，得MACD。MACD通常绘制成围绕零轴线波动的柱形图。MACD柱状大于0涨颜色，小于0跌颜色。
 */
function ModelForecast(
  target_type: number
): IndicatorTemplate<UpDownData> {
  return {
    name: "forecast_modal",
    shortName: "预测结果",
    createTooltipDataSource: (params: object) => {
      const data = get(params, "crosshair.kLineData", {});
      const forecast_outcomes = get(data, "forecast_outcomes", "1");
      const upPercent = get(data, "up", 0);
      const downPercent = get(data, "down", 0);

      // 格式化数值，保持固定宽度
      const formatNumber = (num: number) => {
        const formatter = new Intl.NumberFormat("en-US", {
          minimumIntegerDigits: 2,
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
          useGrouping: false,
        });
        return formatter.format(num).padStart(7, " "); // 7 = 2(整数) + 1(小数点) + 2(小数) + 2(可能的空格)
      };

      const difference = formatNumber(upPercent - downPercent);
      const forecastStatus = get(data, "forecast_status", 1);

      // 根据预测结果确定颜色
      const getResultColor = () => {
        if (target_type === 2) {
          return forecast_outcomes === "1" ? "#00d4aa" : "#ff6b6b";
        } else {
          return forecast_outcomes === "1"
            ? "#888888"
            : forecast_outcomes === "2"
            ? "#00d4aa"
            : "#ff6b6b";
        }
      };

      const getPredictionText = () => {
        if (target_type === 2) {
          return forecast_outcomes === "1" ? " 上涨" : " 下跌";
        } else {
          return forecast_outcomes === "1"
            ? " 震荡"
            : forecast_outcomes === "2"
            ? " 上涨"
            : " 下跌";
        }
      };

      return {
        title: "📊 预测分析",
        name: "预测结果",
        calcParamsText: "",
        icons: [],
        values: [
          {
            title: "📈 预测差值:",
            value: {
              text: difference + '%',
              color: difference.includes("-") ? "#ff6b6b" : "#00d4aa",
            },
          },
          {
            title: "⬆️ 上涨概率:",
            value: {
              text: `${formatNumber(upPercent)}%`,
              color: "#00d4aa",
            },
          },
          {
            title: "⬇️ 下跌概率:",
            value: {
              text: `${formatNumber(downPercent)}%`,
              color: "#ff6b6b",
            },
          },
          {
            title: "🎯 预测方向:",
            value: {
              text: getPredictionText(),
              color: getResultColor(),
            },
          },
          {
            title: "✅ 预测结果:",
            value: {
              text: forecastStatus === 1 ? " 成功" : " 失败",
              color: forecastStatus === 1 ? "#00d4aa" : "#ff6b6b",
            },
          },
        ],
      };
    },
    precision: 2,
    figures: [
      {
        key: "percent",
        title: "预测结果: ",
        type: "bar",
        baseValue: 0,
        styles: (data: IndicatorFigureStylesCallbackData<UpDownData>) => {
          const { prev, current } = data;
          const prevUp = get(prev, "kLineData.up", 0);
          const prevDown = get(prev, "kLineData.down", 0);
          const currentUp = get(current, "kLineData.up", 0);
          const currentDown = get(current, "kLineData.down", 0);
          const prevBar = prevUp - prevDown;
          const currentBar = currentUp - currentDown;

          let color: string;
          let borderColor: string;

          if (currentBar > 0) {
            // 上涨：使用更亮的绿色
            color = currentBar > prevBar ? "#00ffcc" : "#00e6b8";
            borderColor = "#00ffcc";
          } else if (currentBar < 0) {
            // 下跌：使用更亮的红色
            color = currentBar < prevBar ? "#ff8080" : "#ff6666";
            borderColor = "#ff8080";
          } else {
            // 无变化：使用浅灰色
            color = "#a6a6a6";
            borderColor = "#a6a6a6";
          }
          const style =
            prevBar < currentBar ? PolygonType.Stroke : PolygonType.Fill;

          return {
            style,
            color,
            borderColor,
            borderSize: 1,
          };
        },
      },
    ],
    calc: (dataList: KLineData[]) => {
      return dataList.map((item) => {
        const up = get(item, "up", 0);
        const down = get(item, "down", 0);
        return {
          t: item.timestamp,
          up: up,
          down: down,
          percent: up - down,
          open: item.open,
          high: item.high,
          low: item.low,
          close: item.close,
          volume: item.volume,
          turnover: item.turnover,
        };
      });
    },
  };
}

export default ModelForecast;
