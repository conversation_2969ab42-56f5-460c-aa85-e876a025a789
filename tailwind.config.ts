/** @type {import('tailwindcss').Config} */
import type { Config } from "tailwindcss";
import { themeColors } from "./src/app/constants";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: "class", // 简化为只使用 class 模式
  theme: {
    extend: {
      colors: {
        dark: themeColors,
      },
      keyframes: {
        blink: {
          "0%, 100%": { opacity: "0" },
          "50%": { opacity: "1" },
        },
        expand: {
          "0%": { transform: "scale(0,2)", "transform-origin": "right" },
          "100%": { transform: "scale(1)", "transform-origin": "right" },
        },
      },
    },
    animation: {
      blink: "blink 1s ease-in-out infinite",
      expand: "expand 0.3s ease-out forwards",
    },
  },
  plugins: [
    require('tailwind-scrollbar')({ nocompatible: true }),
  ],
};

export default config;
