import db from "@/lib/quantDB";
import { FeatureIndicesDetail } from "@/types/feature";
import moment from "moment";

export interface PaginatedResult<FeatureIndicesDetail> {
  data: FeatureIndicesDetail[];
  total: number;
}

export async function getAllIndexData(
  id: number,
  page: number = 1,
  pageSize: number = 100,
  moreSearch?: Record<string,{
    operator: string;
    value1: string;
    value2?: string;
  }>,
  period?: number
): Promise<PaginatedResult<FeatureIndicesDetail>> {
  try {
    // 查询总数
    let whereClause = " WHERE market_id = ? AND period = ?";
    let queryParams: any[] = [id, period];    // 允许的字段白名单
    const allowedFields = ["o", "h", "l", "c", "v", "vac"];
    // 允许的操作符白名单
    const allowedOperators = ['=', '>', '<', '>=', '<=', 'between'];

    if (moreSearch) {
      Object.entries(moreSearch).forEach(([key, value]) => {
        // 验证字段名是否在白名单中
        if (!allowedFields.includes(key)) {
          throw new Error(`非法的字段名: ${key}`);
        }
        // 验证操作符是否在白名单中
        if (!allowedOperators.includes(value.operator.toLowerCase())) {
          throw new Error(`非法的操作符: ${value.operator}`);
        }

        if (value.operator.toLowerCase() !== 'between') {
          whereClause += ` AND \`${key}\` ${value.operator} ?`;
          if (key === 't') {
            queryParams.push(moment(value.value1, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm:ss'))
          } else {
            queryParams.push(value.value1);
          }
        } else {
          whereClause += ` AND \`${key}\` BETWEEN ? AND ?`;
          if (key === 't') {
            queryParams.push(
              moment(value.value1, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm:ss'),
              moment(value.value2, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm:ss')
            );
          } else {
            queryParams.push(value.value1, value.value2);
          }
        }
      });
    }

    const [countResult] = await db.query(
      `SELECT COUNT(*) as total FROM market_data${whereClause}`,
      queryParams
    );
    const total = (countResult as any)[0].total;

        // 查询分页数据
    const offset = (page - 1) * pageSize;
    const [row] = await db.query(
      `SELECT * FROM market_data${whereClause} 
       ORDER BY market_data.t DESC
       LIMIT ? OFFSET ?`,
      [...queryParams, pageSize, offset]
    );
    return {
      data: row as FeatureIndicesDetail[],
      total,
    };
  } catch (error) {
    console.error("获取指数数据失败:", error);
    throw new Error("获取指数数据失败");
  }
}
