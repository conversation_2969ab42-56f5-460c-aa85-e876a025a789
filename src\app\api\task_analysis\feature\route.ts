import { FeatureRepository } from "./repositories";
import { ApiResponseHandler } from "@/lib/api/response";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const task_id = Number(searchParams.get("task_id"));
  const page = Number(searchParams.get("page")) || 1;
  const pageSize = Number(searchParams.get("pageSize")) || 10;

  try {
    const result = await FeatureRepository.getFeatures(task_id, page, pageSize);
    return ApiResponseHandler.success(result);
  } catch (error) {
    console.error("获取特征数据失败:", error);
    return ApiResponseHandler.error("获取特征数据失败");
  }
}
