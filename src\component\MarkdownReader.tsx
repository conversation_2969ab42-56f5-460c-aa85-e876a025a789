import ReactMarkdown from "react-markdown";
import SyntaxHighlighter from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import remarkGfm from "remark-gfm";

interface MarkdownReaderProps {
  content: string;
}

export default function MarkdownReader({ content }: MarkdownReaderProps) {
  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm]}
      components={{
        h3: ({ node, ...props }) => (
          <h3
            style={{
              fontSize: "1.25rem",
              fontWeight: "bold",
              margin: "1rem 0",
            }}
            {...props}
          />
        ),
        h4: ({ node, ...props }) => (
          <h4
            style={{
              fontSize: "1.1rem",
              fontWeight: "bold",
              margin: "0.8rem 0",
            }}
            {...props}
          />
        ),
        code({
          node,
          inline,
          className,
          children,
          ...props
        }: {
          node?: any;
          inline?: boolean;
          className?: string;
          children?: React.ReactNode;
        }) {
          const match = /language-(\w+)/.exec(className || "");
          const codeContent = String(children).replace(/\n$/, "");

          if (!inline) {
            const cleanCode = codeContent.replace(/^```[\w-]*\n|```$/g, "");
            return (
              <SyntaxHighlighter
                style={vscDarkPlus}
                language={match ? match[1] : "text"}
                PreTag="div"
                {...props}
              >
                {cleanCode}
              </SyntaxHighlighter>
            );
          }

          return (
            <code className={className} {...props}>
              {codeContent}
            </code>
          );
        },
        li({
          node,
          ordered,
          ...props
        }: {
          node?: any;
          ordered?: boolean;
        }) {
          return (
            <li
              {...props}
              className={`ml-4 ${
                ordered ? "list-decimal marker:font-bold" : "list-disc"
              }`}
            />
          );
        },
        ol({ node, ...props }) {
          return <ol {...props} className="ml-4 list-disc" />;
        },
        p({ node, ...props }) {
          return <p {...props} className="mb-2 dark:text-dark-text-200" />;
        },
      }}
    >
      {content}
    </ReactMarkdown>
  );
}