import { NextResponse } from "next/server";
import { factorRepository } from "@/app/api/factor/repositories/factorRepository";
import { ApiResponseHandler } from "@/lib/api/response";

export async function GET() {
  try {
    const types = await factorRepository.getFactorTypes();
    return ApiResponseHandler.success(types);
  } catch (error) {
    console.error("获取因子类型失败:", error);
    return ApiResponseHandler.error("获取因子类型失败", 500);
  }
}
